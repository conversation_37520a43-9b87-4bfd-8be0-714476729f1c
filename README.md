# Intent Action - 智能意图识别与执行框架

本项目演示了一个基于 LangChain 和 LangGraph 构建的智能助手框架。它可以识别用户的自然语言查询意图（如查询行政区划、天气或当前时间），并调用相应的工具（外部 API 或本地函数）来完成任务，最终生成友好的响应。

## ✨ 功能特性

*   **意图识别**: 利用大语言模型 (LLM) 解析用户查询，识别核心意图（例如 `district_query`, `weather_query`, `time_query`）。
*   **实体提取**: 从查询中提取关键信息，如地点名称。
*   **工具调用**:
    *   **高德地图 API**: 查询行政区划信息 (`query_amap_district`) 和天气信息 (`query_amap_weather`)。
    *   **本地函数**: 获取当前系统时间 (`get_current_time`)。
*   **配置驱动**: 通过 `config/tools_config.json` 文件集中管理工具的定义、连接方式和认证信息，方便扩展。
*   **Schema 驱动响应**: 利用工具配置中定义的 `output_schema`，结合工具返回的数据，指导 LLM 生成更准确、更符合上下文的自然语言响应。
*   **状态管理**: 使用 LangGraph 的 `StateGraph` 追踪处理流程中的状态。
*   **灵活的连接器**: 支持通过 `HttpConnector` 调用 REST API，通过 `LocalFunctionConnector` 调用本地 Python 函数。

## 📁 项目结构

项目的核心逻辑围绕 LangGraph 工作流展开，主要文件和目录结构如下：

*   `app/`: 应用核心代码
    *   `adapters/`: 适配器层，处理外部交互（如 API）
        *   `api/main.py`: FastAPI 应用入口和路由定义
    *   `application/`: 应用层，包含核心工作流和业务逻辑
        *   `nodes/`: **LangGraph 节点模块（重构后）**
            *   `intent_parser.py`: 意图解析节点
            *   `district_query.py`: 行政区划查询节点
            *   `weather_query.py`: 天气查询节点
            *   `time_query.py`: 时间查询节点
            *   `user_query.py`: 用户信息查询节点
            *   `response_generator.py`: 响应生成节点
            *   `flow_controller.py`: 流程控制（条件边）
        *   `app.py`: LangGraph 工作流的构建和编译
    *   `core/`: 核心组件，如工具执行器、连接器等
    *   `domain/`: 领域层，定义数据模型和模式（如 Pydantic Schema）
    *   `infrastructure/`: 基础设施层，与外部系统交互（如 Langchain 工具封装）
*   `config/`: 配置文件目录
    *   `tools_config.json`: 工具定义和配置
*   `scripts/`: 辅助脚本
*   `tests/`: 测试代码
*   `.env`: 环境变量配置文件（需自行创建）
*   `requirements.txt`: Python 依赖列表
*   `README.md`: 项目说明文档

## 🔧 环境设置

1.  **克隆仓库**:
    ```bash
    git clone https://github.com/maoplus/intent_action.git
    cd intent_action
    ```

2.  **安装依赖**: (建议在虚拟环境中操作)
    ```bash
    pip install -r requirements.txt.bak
    ```

3.  **配置环境变量**:
    在项目根目录下创建 `.env` 文件，并填入必要的 API 密钥：
    ```dotenv
    # .env 文件内容
    OPENAI_API_BASE="YOUR_OPENAI_API_BASE" # 例如 https://openrouter.ai/api/v1
    OPENAI_API_KEY="YOUR_OPENAI_API_KEY"   # 例如 OpenRouter 的 sk-or-v1-...
    AMAP_API_KEY="YOUR_AMAP_WEB_SERVICE_API_KEY" # 申请的高德 Web 服务 API Key
    ```
    *   `OPENAI_API_KEY`: 用于意图识别和响应生成。这里配置为使用 OpenRouter，你可以替换为其他兼容 OpenAI 的服务。
    *   `AMAP_API_KEY`: 用于调用高德地图的行政区划和天气查询接口。请在高德开放平台申请 Web 服务类型的 Key。

## 🚀 运行应用

1.  **启动服务**:
    ```bash
    ./start.sh
    ```
    或者直接使用 uvicorn:
    ```bash
    uvicorn app.adapters.api.main:app --host 0.0.0.0 --port 8000 --reload
    ```
    服务将在 `http://localhost:8000` 上运行。

2.  **停止服务**:
    *   在前台运行时，按 `Ctrl+C`。
    *   如果需要在后台查找并停止进程：
    ```bash
    lsof -ti:8000 | xargs kill -9
    ```

## 💡 使用示例 (cURL)

通过向 `/api/chat` 端点发送 POST 请求来与助手交互：

*   **查询天气**:
    ```bash
    curl -X 'POST' \
      'http://localhost:8000/api/chat' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d '{
      "source": "test" ,
      "query": "杭州天气怎么样"
    }'
    ```
    **示例响应**: (响应内容可能因实时数据而异)
    ```json
    {
      "response": "好的，杭州市当前天气为晴，气温大约20摄氏度，湿度63%。数据更新时间为2025-04-20 09:30:08。"
    }
    ```

*   **查询行政区划**:
    ```bash
    curl -X 'POST' \
      'http://localhost:8000/api/chat' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d '{
      "query": "查询杭州的行政编码",
      "source":"test"
    }'
    ```
    **示例响应**: (响应内容可能因实时数据而异)
    ```json
    {
      "response": "杭州市的行政区划编码 (adcode) 是 330100。它是一个地级市。"
    }
    ```

*   **查询当前时间**:
    ```bash
    curl -X 'POST' \
      'http://localhost:8000/api/chat' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d '{
      "query": "现在几点了？",
      "source":"test"
    }'
    ```
    **示例响应**: (响应内容将反映服务器当前时间)
    ```json
    {
      "response": "当前时间是：2025-04-20 09:47:00" 
    }
    ```
*   **查询用户信息**:
    ```bash
    curl -X 'POST' \
      'http://localhost:8000/api/chat' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d '{
      "query": "张三是哪里人",
      "source":"test"
    }'
    ```
    **示例响应**: 
    ```json
    {
      "response": "张三的籍贯是北京，他是一位25岁的男士。" 
    }
    ```

*   **无法处理的意图**:
    ```bash
    curl -X 'POST' \
      'http://localhost:8000/api/chat' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d '{
      "query": "帮我订一张去上海的机票",
      "source":"test"
    }'
    ```
    **示例响应**: 
    ```json
    {
      "response": "抱歉，我暂时无法理解您的请求。"
    }
    ```

## 📚 API 文档

### POST /api/chat

用于发送聊天请求并获取响应。

**请求体**:
```json
{
  "query": "string" // 用户的自然语言查询
}
```

**响应**:
```json
{
  "response": "string" // 助手生成的响应文本
}
```

## 🔧 系统扩展指南

本节介绍如何向系统添加新的功能，以用户信息查询功能为例。

### 添加新功能的步骤

1. **更新工具配置**
   - 在 `config/tools_config.json` 中添加新工具的定义
   - 确保定义包含 `name`, `description`, `type`, `execution_details`, `input_schema`, `output_schema` 等必要字段

2. **实现工具函数**
   - 如果是本地函数，在对应的模块中实现函数（如 `app.core.local_functions.py`）
   - 确保函数返回符合标准响应格式的数据（包含 `code`, `message`, `data` 字段）

3. **更新意图识别**
   - 在 `INTENT_PROMPT` 中添加新的意图类别
   - 确保 `parse_intent` 函数能够正确提取和设置相关字段（如 `user_name`）

4. **添加处理节点**
   - 实现处理节点函数（如 `query_user_action`）
   - 在 `get_assistant_app` 函数中注册节点
   - 添加适当的边缘连接

5. **更新响应生成**
   - 在 `generate_response` 函数中添加对新意图的处理
   - 确保能够正确处理工具返回的结果

6. **更新数据模型**
   - 在 `app.domain.schemas.py` 中添加或更新相关的数据模型（如 `AssistantState`）

### 常见问题和解决方案

1. **节点名称不一致问题**
   - 确保在添加节点、定义边缘和路由逻辑时使用一致的节点名称
   - 例如：如果节点名为 `"query_user_action"`，那么在 `add_node`, `add_edge` 和条件边缘中都应使用相同的名称

2. **状态字段缺失问题**
   - 确保在 `AssistantState` 模型中定义了所有需要的字段
   - 如果尝试设置未定义的字段（如 `state.user_info`），将会导致错误

3. **工具返回格式问题**
   - 确保工具函数返回符合标准响应格式的数据
   - 对于本地函数，返回格式应为 `{"code": 0, "message": "Success", "data": {...}}`

4. **响应生成问题**
   - 确保在 `generate_response` 函数中正确处理所有可能的意图和结果
   - 对于新添加的意图，需要添加相应的处理逻辑

## ⚙️ 配置与扩展

*   **工具配置**: `config/tools_config.json` 是核心配置文件。你可以通过修改或添加条目来：
    *   定义新的工具。
    *   指定工具的输入/输出模式 (使用 Pydantic schema)。
    *   配置工具的执行方式（HTTP API、本地函数等）。
    *   管理 API 连接和认证。
*   **本地函数**: 新的本地函数需要在 `app/core/local_functions.py` 中实现，并在 `tools_config.json` 中注册类型为 `function` 的工具。
*   **工作流**: 主要逻辑位于 `app/application/app.py` 中的 `get_assistant_app` 函数，通过 LangGraph 定义状态转换和节点执行。

## 🏗️ 核心组件

*   **`app/application/app.py`**: 定义 LangGraph 工作流，编排意图识别、工具调用和响应生成。
*   **`app/core/executor.py`**: `GenericToolExecutor` 负责解析配置、查找并执行工具。
*   **`app/core/connectors/`**: 包含不同类型工具的连接器实现 (`HttpConnector`, `LocalFunctionConnector` 等)。
*   **`app/domain/schemas.py`**: 定义数据模型 (Pydantic schemas)，如状态 `AssistantState` 和 API 响应模型。
*   **`app/infrastructure/langchain_tools.py`**: 将 `GenericToolExecutor` 包装成 LangChain 可识别的工具。
*   **`config/tools_config.json`**: 工具和服务连接的配置文件。