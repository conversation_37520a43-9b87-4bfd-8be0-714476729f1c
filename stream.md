# LangGraph + FastAPI 流式输出实现：经验总结

本文档记录了在 FastAPI 应用中集成 LangGraph 并实现端到端流式输出（Server-Sent Events, SSE）过程中遇到的关键挑战和解决方案。

## 目标

*   支持 LangGraph 中多个节点（例如，意图识别、最终回复生成）调用 LLM 并产生流式输出。
*   将这些流式块（如思考过程、生成的内容块）实时发送给前端。
*   在整个 LangGraph 流程结束后，通过最后一个 SSE 事件发送完整的、格式化后的最终结果（类似于非流式请求的响应）。

## 关键组件

*   **FastAPI Endpoint (`/api/chat` in `main.py`)**: 接收请求，区分流式 (`chatType: "stream"`)与非流式请求。
*   **LangGraph Application (`current_app` in `main.py`)**: 执行业务逻辑图。
*   **LLM 调用函数 (`call_llm_api` in `direct_llm.py`)**: 封装对 LLM API 的调用，支持流式响应。
*   **流处理函数 (`_handle_stream` in `direct_llm.py`)**: 处理来自 LLM API 的单个流，将事件放入队列。
*   **队列 (`llm_stream_queue`, `sse_event_queue` in `main.py`)**: 用于在同步/异步任务间传递事件。
    *   `llm_stream_queue` (`queue.Queue`): 线程安全，接收来自 `_handle_stream` 的事件。
    *   `sse_event_queue` (`asyncio.Queue`): 异步，供 SSE 生成器消费。
*   **协调任务 (`run_graph_and_process` in `main.py`)**: 驱动 LangGraph 执行，管理上下文，发送最终完成/错误信号。
*   **中继任务 (`relay_queues` in `main.py`)**: 从 `llm_stream_queue` 读取事件，放入 `sse_event_queue`。
*   **SSE 生成器 (`event_generator` in `main.py`)**: 从 `sse_event_queue` 读取事件，格式化为 SSE 并发送给客户端。
*   **上下文变量 (`stream_queue_var` in `direct_llm.py`)**: 允许 `call_llm_api` 在正确的请求上下文中访问 `llm_stream_queue`。

## 遇到的挑战与解决方案

1.  **挑战：区分流式/非流式请求**
    *   **问题**: API 需要同时支持立即返回完整结果和流式返回中间过程+最终结果。
    *   **解决方案**: 在请求体 (`AssistantAppParams`) 中添加 `chatType` 字段。在 `/api/chat` 端点中根据 `chatType == "stream"` 执行不同的处理逻辑。

2.  **挑战：从同步 LLM 调用传递事件到异步 SSE**
    *   **问题**: LangGraph 节点内的 `call_llm_api` 可能在同步线程中执行（如果使用 `invoke`），或者 LLM SDK 的流式处理本身是同步迭代器，而 FastAPI 的 SSE 生成器 (`event_generator`) 是异步的。
    *   **解决方案**:
        *   使用两个队列：一个线程安全的 `queue.Queue` (`llm_stream_queue`) 和一个 `asyncio.Queue` (`sse_event_queue`)。
        *   使用 `ContextVar` (`stream_queue_var`) 将 `llm_stream_queue` 传递给 `call_llm_api` 的上下文，使其能将事件放入正确的队列。
        *   创建一个异步的 `relay_queues` 任务，它使用 `loop.run_in_executor` 从 `llm_stream_queue` 安全地获取事件，并 `await sse_q.put(item)` 将其放入 `sse_event_queue`。

3.  **挑战：队列积压导致事件丢失 (`Stream queue is full`)**
    *   **问题**: LLM 生成事件的速度可能快于 SSE 发送的速度，导致 `llm_stream_queue` 或 `sse_event_queue` 变满，后续事件被丢弃。
    *   **解决方案**: 在 `main.py` 的 `chat` 函数中，创建队列时增大 `maxsize` (例如，从 100 增加到 1000)。这提供了更大的缓冲。

4.  **挑战：流在第一个流式节点后就提前终止**
    *   **问题**: 客户端只收到第一个节点的流式事件，然后连接就关闭了，收不到后续节点的事件。
    *   **根本原因**: `_handle_stream` 函数在处理完 *其自身* 的 LLM 流后，错误地向 `llm_stream_queue` 发送了 `COMPLETE` 事件信号。这个信号被 `relay_queues` 传递给 `sse_event_queue`，最终导致 `event_generator` 认为整个流程结束而提前关闭连接。
    *   **解决方案**: 修改 `_handle_stream`，移除其 `finally` 块中发送 `COMPLETE` 事件的逻辑。**只有** `run_graph_and_process` 函数（在整个 LangGraph 执行完毕后）才应该发送最终的 `COMPLETE` 或 `ERROR` 事件，以及用于停止 `relay_queues` 的 `None` 哨兵信号。

5.  **挑战：在流结束时发送完整的格式化结果**
    *   **问题**: 用户期望在流式传输结束后，通过最后的 `COMPLETE` 事件接收到与非流式请求一样的、包含所有格式化数据（如 CRF 表单）的完整响应体。
    *   **解决方案**: 修改 `run_graph_and_process` 函数的 `finally` 块。在发送 `COMPLETE` 事件之前，调用与非流式路径相同的辅助函数（如 `getCrfResults`, `getKnowledage`）来处理 `final_result`，构建完整的 `CustomApiResponse` 对象，并将其作为 `COMPLETE` 事件的 `data` 负载发送。

## 未来注意事项

*   **错误处理**: 确保所有异步任务（`run_graph_and_process`, `relay_queues`, `event_generator`）都有健壮的错误处理和日志记录。
*   **任务清理**: `event_generator` 的 `finally` 块负责取消 `run_task` 和 `relay_task`，确保资源被正确释放。
*   **上下文管理**: 必须在 `run_graph_and_process` 的 `finally` 块中正确重置 `stream_queue_var` 上下文变量。
*   **LangGraph 流式方法**: 当前实现使用 `current_app.invoke` 配合 `ContextVar` 和队列的“副作用”来实现流式。未来可以探索 LangGraph 的原生流式方法（如 `astream_events`, `astream_log`），看是否能简化实现。但需要注意这些方法如何与多节点流式输出以及现有队列机制集成。