#FROM harbor.primelifescience.com.cn/prime-infra/python:3.9-slim
FROM harbor.primelifescience.com.cn/prime-direct/prime-rag-api:dev_299947-250609_165741

# 安装构建所需的工具和库
RUN apt-get -o Acquire::AllowInsecureRepositories=true -o Acquire::AllowDowngradeToInsecureRepositories=true update && \
    apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        libffi-dev \
        libssl-dev \
        python3-venv \
        build-essential \
        git \
        curl \
        && rm -rf /var/lib/apt/lists/*

# 设置 pip 清华源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 设置工作目录
WORKDIR /app
COPY . .

# 创建虚拟环境并安装依赖
RUN python -m venv /app/venv && \
    /app/venv/bin/pip install --upgrade pip setuptools wheel && \
    /app/venv/bin/pip install -r requirements.txt

# 设置环境变量
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PATH="/app/venv/bin:$PATH"

EXPOSE 8000

CMD ["python", "run.py"]
