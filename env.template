# =============================================================================
# 环境配置文件模板
# 复制此文件为 .env 并根据您的需求修改配置值
# 命令: cp env.template .env
# =============================================================================

# --- OpenRouter API 配置 ---
# OpenRouter API 密钥 (必需)
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here

# OpenRouter API 基础URL (通常不需要修改)
OPENROUTER_API_BASE=https://openrouter.ai/api/v1

# --- OpenRouter 供应商配置 ---
# 供应商优先顺序 (逗号分隔，按优先级排序)
# 可选值: Anthropic, Together, OpenAI, Groq, Fireworks, Nebius, Hyperbolic 等
OPENROUTER_PROVIDER_ORDER=Anthropic,Together

# 是否允许备用供应商 (true/false)
# false: 禁用备用供应商，只使用指定的优先顺序
# true: 允许使用备用供应商
OPENROUTER_ALLOW_FALLBACKS=false

# 忽略的供应商列表 (逗号分隔)
# 这些供应商将不会被使用
OPENROUTER_PROVIDER_IGNORE=Fireworks,Nebius,Hyperbolic

# OpenRouter 最大token数量
OPENROUTER_MAX_TOKENS=32000

# --- LLM 模型配置 ---
# 主要使用的LLM模型
LLM_MODEL_NAME=anthropic/claude-3-opus:beta

# OpenRouter 默认模型 (当没有指定模型时使用)
OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-chat

# LLM 提供商 (openrouter/deepseek)
LLM_PROVIDER=openrouter

# LLM 温度参数 (0.0-1.0)
LLM_TEMPERATURE=0

# 快速LLM模型 (用于意图识别等快速任务)
FLASH_LLM_MODEL_NAME=deepseek/deepseek-chat

# --- 文本分析专用配置 ---
# 文本分析API提供商
ANALYZE_TEXT_LLM_PROVIDER=openrouter

# 文本分析API模型
ANALYZE_TEXT_LLM_MODEL_NAME=deepseek/deepseek-r1

# 文本分析API密钥 (如果与主API不同)
ANALYZE_TEXT_LLM_API_KEY=sk-or-v1-your-api-key-here

# 文本分析API基础URL
ANALYZE_TEXT_LLM_API_BASE=https://openrouter.ai/api/v1

# --- OpenRouter 请求头配置 ---
# HTTP Referer (可选)
HTTP_REFERER=http://localhost

# X-Title (可选)
X_TITLE=Prime RAG API

# --- Milvus 向量数据库配置 ---
# 是否使用远程Milvus (true/false)
MILVUS_USE_REMOTE=false

# Milvus 集合名称
MILVUS_COLLECTION_NAME=icd10_knowledge

# 本地Milvus Lite 数据文件路径
MILVUS_LITE_FILE=./milvus_lite_data.db

# 远程Milvus配置 (当MILVUS_USE_REMOTE=true时需要)
# MILVUS_REMOTE_URI=your-milvus-uri
# MILVUS_REMOTE_TOKEN=your-milvus-token

# Milvus 批量插入大小
MILVUS_INSERT_BATCH_SIZE=50

# --- 嵌入模型配置 ---
# 嵌入模型名称
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2

# 是否使用HuggingFace远程嵌入 (true/false)
USE_HF_REMOTE_EMBEDDING=false

# HuggingFace API Token (当USE_HF_REMOTE_EMBEDDING=true时需要)
# HF_API_TOKEN=your-hf-token

# --- 数据处理配置 ---
# 输入CSV文件路径
# INPUT_CSV_PATH=./data/icd10cm_codes_2025.csv

# 处理批量大小
PROCESSING_BATCH_SIZE=100

# --- 提示词文件配置 (必需) ---
# 主提示词文件路径
PROMPT_FILE_PATH=/path/to/your/prompts/prompt_en.md

# 查询提示词文件路径
QUERY_PROMPT_FILE_PATH=/path/to/your/prompts/query_prompt.md

# --- DashScope 配置 (可选，如果使用阿里云DashScope) ---
# DASHSCOPE_API_KEY=your-dashscope-api-key
# DASHSCOPE_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1

# --- 结构化分析服务配置 ---
# 结构化分析API的基础URL
STRUCTURING_API_URL=http://172.25.1.19:8211/structuring

# =============================================================================
# 配置说明:
# 
# 1. OpenRouter 供应商优先顺序配置示例:
#    - 只使用 Anthropic: OPENROUTER_PROVIDER_ORDER=Anthropic
#    - 优先 Anthropic，备选 Together: OPENROUTER_PROVIDER_ORDER=Anthropic,Together
#    - 多个供应商: OPENROUTER_PROVIDER_ORDER=Anthropic,Together,OpenAI,Groq
#
# 2. 备用供应商控制:
#    - 禁用备用: OPENROUTER_ALLOW_FALLBACKS=false
#    - 启用备用: OPENROUTER_ALLOW_FALLBACKS=true
#
# 3. 忽略供应商示例:
#    - 忽略不稳定的供应商: OPENROUTER_PROVIDER_IGNORE=Fireworks,Nebius
#    - 不忽略任何供应商: OPENROUTER_PROVIDER_IGNORE=
#
# 4. Token 限制:
#    - 根据您的需求和模型能力调整 OPENROUTER_MAX_TOKENS
#    - 常见值: 4096, 8192, 16384, 32000, 128000
#
# 5. 常用供应商组合推荐:
#    - 高质量优先: OPENROUTER_PROVIDER_ORDER=Anthropic,OpenAI
#    - 速度优先: OPENROUTER_PROVIDER_ORDER=Groq,Together
#    - 平衡选择: OPENROUTER_PROVIDER_ORDER=Anthropic,Together,OpenAI
#    - 成本优化: OPENROUTER_PROVIDER_ORDER=Together,Groq
# ============================================================================= 