# Function Calling/Tool Use Schema
 
本文档定义了本项目中工具（Functions/Tools）的配置规范和执行后的统一返回格式。所有工具的配置均在 `config/tools_config.json` 文件中定义。

## 1. 统一返回格式模板

为了方便 LangGraph 工作流处理和状态更新，所有工具（无论是 REST API 调用还是本地函数执行）在通过 `GenericToolExecutor` 执行后，都应返回符合以下 Pydantic `BaseResponse` 结构的 JSON 对象：

```python
from pydantic import BaseModel, Field
from typing import Optional, Any

class BaseResponse(BaseModel):
    """工具执行结果的基础响应模型"""
    code: int = Field(..., description="状态码，0 表示成功，非 0 表示失败")
    message: str = Field(..., description="状态信息，成功时通常为 'OK'，失败时为错误描述")
    data: Optional[Any] = Field(None, description="成功时返回的具体数据，失败时为 null")

# 示例（成功）
# {
#   "code": 0,
#   "message": "OK",
#   "data": { ... } # 具体业务数据
# }

# 示例（失败）
# {
#   "code": 400,
#   "message": "Missing required parameter: city",
#   "data": null
# }
```

*   `code`: 整数类型。`0` 代表成功，其他非零值代表不同类型的错误（可以是 HTTP 状态码，也可以是自定义错误码）。
*   `message`: 字符串类型。提供关于执行状态的文本描述。
*   `data`: 任意类型（通常是字典或列表，或 `None`）。包含成功执行时返回的实际数据。如果执行失败或没有数据返回，则为 `null`。

**注意**: 对于直接调用外部 API 的 `rest` 类型工具，`GenericToolExecutor` 会尝试将原始 API 响应转换为上述 `BaseResponse` 格式。对于 `function` 类型工具，函数本身需要实现逻辑来返回符合此格式的字典。

## 2. `config/tools_config.json` 结构

该文件包含两个主要部分：`connections` 和 `tools`。

### 2.1 Connections

`connections` 数组用于定义和管理对外部服务的认证信息。

```json
"connections": [
  {
    "id": "gaode_api_main", // 连接的唯一标识符
    "type": "api_key",       // 认证类型，目前支持 "api_key"
    "credentials": {
      "api_key_env_var": "AMAP_API_KEY" // 存储 API Key 的环境变量名称
    },
    "metadata": {
      "display_name": "高德地图主要API Key" // 可选的显示名称
    }
  }
  // ... 可以添加更多连接
]
```

*   `id`: 每个连接的唯一 ID，工具配置中通过 `connection_id` 引用。
*   `type`: 目前支持 `api_key`。
*   `credentials`: 包含认证所需的具体信息。
    *   `api_key_env_var`: 指定从哪个环境变量读取 API Key。这确保了敏感信息不被硬编码。
*   `metadata`: 可选的元数据，如 `display_name`。

### 2.2 Tools

`tools` 数组定义了所有可用的工具。每个工具对象包含以下通用字段：

*   `name`: (string) 工具的唯一名称，用于在代码中引用和调用。
*   `description`: (string) 工具功能的自然语言描述，主要供 LLM 理解工具用途。
*   `type`: (string) 工具的类型，决定了其配置和执行方式。目前支持：
    *   `rest`: 调用外部 RESTful API。
    *   `function`: 执行本地 Python 函数。
*   `metadata`: (object, optional) 包含工具的附加信息，如：
    *   `display_name`: (string) 用户友好的工具名称。
    *   `category`: (string) 工具分类。
    *   `labels`: (array of strings) 相关标签。
    *   `doc_url`: (string) 相关文档链接。

#### 2.2.1 `rest` 类型工具配置

此类工具通过 HTTP 调用外部 API。其配置核心是 `openapi` 字段，它使用 OpenAPI 规范 (Swagger) 的子集来描述 API 端点。

```json
{
  "name": "query_amap_weather",
  "description": "查询指定城市编码(adcode)的实时或预报天气信息。",
  "type": "rest",
  "connection_id": "gaode_api_main", // 引用上面定义的 connection
  "openapi": {
    "servers": [ // API 服务器基础 URL
      { "url": "https://restapi.amap.com/v3" }
    ],
    "paths": { // API 路径定义
      "/weather/weatherInfo": { // 具体的 API 路径
        "get": { // HTTP 方法
          "summary": "查询天气信息",
          "operationId": "queryWeather", // 操作 ID
          "security": [ { "apiKey": [] } ], // 安全性定义，这里表示需要 API Key
          "parameters": [ // API 参数列表
            {
              "name": "city", // 参数名
              "in": "query", // 参数位置 (query, path, header, cookie)
              "required": true, // 是否必须
              "description": "...",
              "schema": { "type": "string" } // 参数类型
            },
            {
              "name": "extensions",
              "in": "query",
              "schema": { "type": "string", "default": "base", "enum": ["base", "all"] }
            },
            {
              "name": "key", // API Key 参数
              "in": "query",
              "required": true,
              "schema": { "type": "string" },
              "x-is-api-key": true // 特殊标记，指示这是 API Key 参数
            }
          ],
          "responses": { // API 响应定义 (主要用于文档，执行器不强制校验)
            "200": {
              "description": "成功响应",
              "content": { "application/json": { "schema": { ... } } }
            }
          }
        }
      }
    }
  },
  "metadata": { ... }
}
```

*   `connection_id`: 引用 `connections` 中定义的 ID，用于获取认证凭证（如 API Key）。
*   `openapi`:
    *   `servers`: 定义 API 的基础 URL。
    *   `paths`: 定义 API 端点。
        *   路径 (`/weather/weatherInfo`): 对应 API 的具体路径。
        *   HTTP 方法 (`get`): 定义请求方法。
        *   `parameters`: 定义请求参数，包括名称、位置 (`in`)、是否必需 (`required`) 和类型 (`schema`)。
            *   `x-is-api-key: true`: 一个自定义扩展字段，明确标记哪个参数是 API Key，`GenericToolExecutor` 会自动将 `connection_id` 对应的 Key 填入此参数。
        *   `responses`: 定义预期的响应格式（可选，主要用于文档）。

#### 2.2.2 `function` 类型工具配置

此类工具执行项目内部定义的 Python 函数。

```json
{
  "name": "get_current_time",
  "description": "获取服务器当前的日期和时间。",
  "type": "function",
  "execution_details": {
    "module_path": "app.core.local_functions", // 函数所在的 Python 模块路径
    "function_name": "get_current_time_func" // 要执行的函数名称
  },
  "input_schema": { // 函数的输入参数 Pydantic 模型 (可选)
    "type": "object",
    "properties": {} // 此函数无输入参数
  },
  "output_schema": { // 函数期望返回值的 Pydantic 模型 (必填，用于文档和可能的验证)
    "type": "object",
    "properties": {
      "code": { "type": "integer" },
      "message": { "type": "string" },
      "data": { "type": "string", "format": "date-time" } // 示例：期望 data 是时间字符串
    },
    "required": ["code", "message"]
  },
  "metadata": { ... }
}
```

*   `execution_details`:
    *   `module_path`: 指定包含目标函数的 Python 模块的完整点分路径 (e.g., `app.core.local_functions`)。
    *   `function_name`: 指定要调用的函数名称 (e.g., `get_current_time_func`)。
*   `input_schema`: (可选) 定义函数期望接收的参数的 JSON Schema。如果函数不需要参数，可以像示例中一样定义一个空的 `properties`。`GenericToolExecutor` 会根据这个 schema 验证传入的参数。
*   `output_schema`: (必填) 定义函数期望返回的数据结构的 JSON Schema。**重要**: 即使定义了 `output_schema`，实际执行的本地函数也必须自己确保返回符合第一节 **统一返回格式模板** 的字典。`output_schema` 主要用于文档目的或未来可能的自动验证。当 output_schema 类型为 object，不能省略里面的 properties。

## 3. 总结

通过 `tools_config.json` 和统一的返回格式，我们可以方便地管理和扩展各种类型的工具，并确保它们能够无缝集成到 LangGraph 工作流中。添加新工具通常只需要修改配置文件，并在必要时实现本地函数即可。