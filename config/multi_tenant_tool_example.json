{"connections": [{"id": "research_a_api_key", "type": "api_key", "credentials": {"api_key_env_var": "RESEARCH_A_API_KEY"}}, {"id": "research_b_api_key", "type": "api_key", "credentials": {"api_key_env_var": "RESEARCH_B_API_KEY"}}, {"id": "research_c_api_key", "type": "api_key", "credentials": {"api_key_env_var": "RESEARCH_C_API_KEY"}}], "tools": [{"name": "query_clinical_protocol", "description": "查询临床研究方案信息", "type": "rest", "multi_tenant": true, "openapi": {"servers": [{"url": "https://api.default.example.com/v1"}], "paths": {"/protocols/{protocol_id}": {"get": {"operationId": "getProtocol", "parameters": [{"name": "protocol_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tenant_id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "X-API-Key", "in": "header", "required": true, "x-is-api-key": true, "schema": {"type": "string"}}]}}}}}, {"name": "query_visit_schedule", "description": "查询临床研究访视计划", "type": "rest", "multi_tenant": true, "openapi": {"servers": [{"url": "https://api.default.example.com/v1"}], "paths": {"/protocols/{protocol_id}/visits": {"get": {"operationId": "getVisitSchedule", "parameters": [{"name": "protocol_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "subject_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "tenant_id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "X-API-Key", "in": "header", "required": true, "x-is-api-key": true, "schema": {"type": "string"}}]}}}}}]}