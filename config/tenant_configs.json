{"tenant1": {"name": "研究机构A", "api_base_url": "https://api.research-a.example.com/v1", "connection_id": "research_a_api_key", "settings": {"default_language": "zh-CN", "timezone": "Asia/Shanghai"}}, "tenant2": {"name": "研究机构B", "api_base_url": "https://api.research-b.example.com/v1", "connection_id": "research_b_api_key", "settings": {"default_language": "en-US", "timezone": "America/New_York"}}, "tenant3": {"name": "研究机构C", "api_base_url": "https://api.research-c.example.com/v1", "connection_id": "research_c_api_key", "settings": {"default_language": "zh-CN", "timezone": "Asia/Shanghai"}}}