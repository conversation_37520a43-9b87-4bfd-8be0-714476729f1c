{"connections": [{"id": "gaode_api_main", "type": "api_key", "credentials": {"api_key_env_var": "AMAP_API_KEY"}, "metadata": {"display_name": "高德地图主要API Key"}}], "tools": [{"name": "query_amap_district", "description": "根据关键词(如城市名、adcode)查询高德地图行政区划信息，获取下级行政区列表和adcode。", "type": "rest", "connection_id": "gaode_api_main", "openapi": {"servers": [{"url": "https://restapi.amap.com/v3"}], "paths": {"/config/district": {"get": {"summary": "查询行政区划", "operationId": "queryDistrict", "security": [{"apiKey": []}], "parameters": [{"name": "keywords", "in": "query", "required": true, "description": "查询的关键词，如城市名、adcode 或 citycode", "schema": {"type": "string"}}, {"name": "subdistrict", "in": "query", "description": "查询的子行政区级数 (0-3)", "schema": {"type": "integer", "default": 1}}, {"name": "key", "in": "query", "required": true, "description": "高德API Key", "schema": {"type": "string"}, "x-is-api-key": true}], "responses": {"200": {"description": "成功的响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"districts": {"type": "array", "description": "行政区列表", "items": {"type": "object"}}, "count": {"type": "string"}, "suggestion": {"type": "object"}}}}}}}}}}}, "metadata": {"display_name": "高德行政区划查询", "category": "location", "labels": ["geography", "district", "china"], "doc_url": "https://lbs.amap.com/api/webservice/guide/api/district"}}, {"name": "query_amap_weather", "description": "查询指定城市编码(adcode)的实时或预报天气信息。", "type": "rest", "connection_id": "gaode_api_main", "openapi": {"servers": [{"url": "https://restapi.amap.com/v3"}], "paths": {"/weather/weatherInfo": {"get": {"summary": "查询天气信息", "operationId": "query<PERSON><PERSON><PERSON>", "security": [{"apiKey": []}], "parameters": [{"name": "city", "in": "query", "required": true, "description": "需要查询天气城市的 adcode", "schema": {"type": "string"}}, {"name": "extensions", "in": "query", "description": "气象类型。base:实况, all:预报", "schema": {"type": "string", "default": "base", "enum": ["base", "all"]}}, {"name": "key", "in": "query", "required": true, "description": "高德API Key", "schema": {"type": "string"}, "x-is-api-key": true}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"lives": {"type": "array", "description": "实况天气列表", "items": {"type": "object"}}, "forecasts": {"type": "array", "description": "预报天气列表", "items": {"type": "object"}}}}}}}}}}}}, "metadata": {"display_name": "高德天气查询", "category": "weather", "labels": ["weather", "forecast", "china"], "doc_url": "https://lbs.amap.com/api/webservice/guide/api/weatherinfo"}}, {"name": "get_current_time", "description": "获取服务器当前的日期和时间。", "type": "function", "execution_details": {"module_path": "app.core.local_functions", "function_name": "get_current_time_func"}, "input_schema": {"type": "object", "properties": {}, "required": [], "description": "无需入参"}, "output_schema": {"type": "string", "description": "格式为 '%Y年%m月%d日 %H时%M分%S秒' 的当前时间字符串", "example": "2025年4月20日 10时25分30秒"}}, {"name": "query_user_info", "description": "根据用户姓名查询用户的性别、年龄和籍贯信息 (<PERSON><PERSON>)。", "type": "function", "execution_details": {"module_path": "app.core.local_functions", "function_name": "query_user_info"}, "input_schema": {"type": "object", "properties": {"name": {"type": "string", "description": "要查询的用户姓名。"}}, "required": ["name"]}, "output_schema": {"type": "object", "description": "工具执行的标准响应格式。", "properties": {"code": {"type": "integer", "description": "状态码，0 表示成功，非 0 表示失败 (例如 404 表示未找到)"}, "message": {"type": "string", "description": "状态信息，例如 'Success' 或 '用户未找到'"}, "data": {"type": ["object", "null"], "description": "成功找到用户时包含用户信息，否则为 null 或包含错误提示对象。", "properties": {"gender": {"type": "string", "description": "用户性别"}, "age": {"type": "integer", "description": "用户年龄"}, "origin": {"type": "string", "description": "用户籍贯"}}, "required": ["gender", "age", "origin"], "additionalProperties": false}}, "required": ["code", "message"]}, "metadata": {"display_name": "查询用户信息 (<PERSON><PERSON>)", "category": "MockData", "labels": ["user", "mock", "local"]}}]}