# 背景

- 打造专为临床研究协调员（CRC）和临床研究助理（CRA）设计的智能 AI 助手
- 该项目是面向多租户（Multi-Tenant）的 SaaS 服务

## DDD 分层规范

```text
project_root/
├── app/                            # 主应用包，所有业务代码
│   ├── __init__.py
│   ├── domain/                     # 领域层: 领域模型、聚合根、实体、值对象
│   │   ├── __init__.py
│   │   └── protocol.py
│   ├── application/                # 应用层: 服务、用例、业务流程/服务协调
│   │   ├── __init__.py
│   │   └── assistant_service.py
│   ├── infrastructure/             # 基础设施层: 第三方接口、数据库、文件、外部 API
│   │   ├── __init__.py
│   │   └── openai_client.py
│   ├── adapters/                   # 接口/适配器层: Web、CLI、Batch等接口
│   │   ├── __init__.py
│   │   ├── api/                    # API 接口 (FastAPI/Flask等)
│   │   └── cli.py
│   ├── shared/                     # 通用代码: 工具、常量、基础类型
│   │   ├── __init__.py
│   │   └── utils.py
│   └── config.py                   # 配置管理
│
├── tests/                          # 测试
│   ├── domain/
│   ├── application/
│   ├── infrastructure/
│   └── adapters/
│
├── scripts/                        # 运维、初始化脚本等
│
├── requirements.txt                # Python依赖
├── README.md
└── pyproject.toml                  # 构建工具（推荐用 poetry/pdm/等现代工具）
```

## 核心组件抽象

项目已高度抽象出以下五个核心组件：

1.  **知识库 (Knowledge Base):**
    *   **内容:** 临床试验方案 (含修正案)、受试者信息 (需高度关注隐私安全与合规)、药物信息 (用法、AE/SAE 定义、PV)、规范指南 (GCP, 法规, SOP)、站点信息、EDC/CTMS 操作知识等。
    *   **作用:** 贯穿全局，用于辅助意图识别、辅助触发 Action、以及作为 RAG (Retrieval-Augmented Generation) 的基础来生成知识增强的答案。
    *   **关键技术:** RAG, 向量数据库索引，结构化与非结构化数据处理，知识更新与版本控制机制。
    *   **特别注意:** 受试者信息 (PHI/PII) 的处理必须符合最高安全和隐私标准 (如脱敏、权限控制、安全 API 访问)。

2.  **意图 (Intent):**
    *   **定义:** 识别用户查询背后的目的或想要执行的任务。
    *   **特点:** 可能包含简单可枚举的意图 (如“查询访视窗口”) 和复杂的、需要推理的意图 (如“判断此情况是否为 SAE 及报告时限”)。LLM 在处理复杂、模糊意图方面有优势。
    *   **实现:** 可利用 LLM 结合知识库上下文进行意图识别，输出结构化意图表示。需考虑多轮对话中的上下文依赖。
    *   **示例:** 查询方案细节、查询受试者状态、辅助填写 eCRF、计算访视窗口、核对入排标准、指导 SOP 流程等。

3.  **Action (Action):**
    *   **定义:** 系统根据识别到的意图所执行的操作集合。
    *   **实现:** 通常映射为对内部/外部系统 (EDC, CTMS, 日历等) 的 API 调用、内部函数执行 (如计算)、或生成需要用户确认的建议/预填信息 (人机协同)。
    *   **关键考量:**
        *   **安全与权限:** 严格的权限控制。
        *   **可靠性:** 幂等性设计、错误处理、回滚机制。
        *   **审计:** 所有 AI 触发或辅助的操作必须有详细日志。
    *   **示例:** `get_subject_visit_schedule`, `check_inclusion_criteria`, `prefill_crf_page` (需确认), `calculate_next_visit_window`, `draft_query_email`。

4.  **用户查询 (User Query):**
    *   **形式:** CRC/CRA 输入的原始文本，可能包含专业术语、缩写、口语化表达。
    *   **处理:** 需要强大的自然语言理解 (NLU) 能力 (LLM 优势)，支持多轮对话以澄清和收集信息，并感知对话上下文。

5.  **系统响应 (System Response):**
    *   **要求:**
        *   **知识增强:** 基于从知识库检索到的信息生成，最好能提供信息来源以增加可信度。
        *   **结构化:** 清晰、简洁，使用列表、表格等格式化输出。
        *   **准确与安全:** 避免误导，对无法确定的问题应明确告知。对于涉及临床判断的建议，需包含免责声明，强调最终决策由专业人员做出。
    *   **改进:** 包含用户反馈机制。

## 技术实现框架选型

*   **框架:** Langchain / LangGraph
*   **理由:**
    *   **组件映射:** Langchain 提供了与上述核心组件良好对应的模块 (Retriever, PromptTemplate, OutputParser, Tool, Memory)。
    *   **流程控制:** LangGraph 特别适合构建复杂、包含条件分支和状态管理的 Agent 流程，能更精确地控制多步推理和人机交互。
    *   **生态成熟:** 拥有丰富的集成和工具。

## LLM 接口规范

- 仅考虑 OpenAI 兼容接口
- 测试过程会用到 OpenRouter
- 杜绝不兼容 OpenAI 接口的实现，例如 anthropic、google gemini 的自定义实现

## 任务
- 我们小试牛刀，用Langchain / LangGraph实现俩小助手，提供一个简单的 web 页面即可

### 任务1: 高德行政区域查询
- 用户说:"我在杭州，这个城市的行政区划是什么？或者杭州行政区划"

#### 请改造成 tool use
- 入参

| 参数名 | 含义 | 规则说明                                                                                                                                                                                                   | 是否必须 | 缺省值 |
|-------|-----|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|-------|
| key | 请求服务权限标识 | 这里写死                                                                                                                                                                                                   | 必填 | 无 |
| keywords | 查询关键字 | 规则：只支持单个关键词语搜索<br>关键词支持：行政区名称、citycode、adcode<br>例如，在 subdistrict=2，搜索省份(例如山东)，能够显示市(例如济南)，区(例如历下区)<br>adcode 信息可参考 [城市编码表](https://lbs.amap.com/api/webservice/guide/api/district) 获取                 | 可选 | 无 |
| subdistrict | 子级行政区 | 规则：设置显示下级行政区级数(行政区级别包括：国家、省/直辖市、市、区/县、乡镇/街道多级数据)<br>可选值：0、1、2、3等数字，并以此类推<br>0：不返回下级行政区；<br>1：返回下一级行政区；<br>2：返回下两级行政区；<br>3：返回下三级行政区；<br>需要在此特殊说明，目前部分城市和省直辖县因为没有区县的概念，故在市级下方直接显示街道。<br>例如：广东-东莞、海南-文昌市 | 可选 | 1 |
- 
- 调用方式
```curl
curl --location 'https://restapi.amap.com/v3/config/district?keywords=%E6%9D%AD%E5%B7%9E&subdistrict=1&key=fa21eddea481b788af3458dc509364b2' \
--header 'Content-Type: application/json' \
--header 'X-API-KEY: pre-d98df0d7-8dd1'
```
- 出参

| 名称 | 含义 | 规则说明 |
|------|------|----------|
| status | 返回结果状态值 | 值为0或1，0表示失败；1表示成功 |
| info | 返回状态说明 | 返回状态说明，status 为0时，info 返回错误原因，否则返回"OK"。 |
| infocode | 状态码 | 返回状态说明，10000代表正确，详情参阅 info 状态表 |
| suggestion | 建议结果列表 | |
| &nbsp;&nbsp;keywords | 建议关键字列表 | |
| &nbsp;&nbsp;cities | 建议城市列表 | |
| districts | 行政区列表 | |
| &nbsp;&nbsp;district | 行政区信息 | |
| &nbsp;&nbsp;&nbsp;&nbsp;citycode | 城市编码 | |
| &nbsp;&nbsp;&nbsp;&nbsp;adcode | 区域编码 | 街道没有独有的 adcode，均继承父类(区县)的 adcode |
| &nbsp;&nbsp;&nbsp;&nbsp;name | 行政区名称 | |
| &nbsp;&nbsp;&nbsp;&nbsp;polyline | 行政区边界坐标点 | 当一个行政区范围，由完全分隔两块或者多块的地块组成，每块地的 polyline 坐标串以 \| 分隔。<br>如北京的朝阳区 |
| &nbsp;&nbsp;&nbsp;&nbsp;center | 区域中心点 | 乡镇级别返回的center是边界线上的形点，其他行政级别返回的center不一定是中心点，若政府机构位于面内，则返回政府坐标，政府不在面内，则返回繁华点坐标。 |
| &nbsp;&nbsp;&nbsp;&nbsp;level | 行政区划级别 | country：国家<br>province：省份(直辖市会在province显示)<br>city：市(直辖市会在province显示)<br>district：区县<br>street：街道 |
| &nbsp;&nbsp;&nbsp;&nbsp;districts | 下级行政区列表，包含 district 元素 | |

json demo
```json
{
    "status": "1",
    "info": "OK",
    "infocode": "10000",
    "count": "1",
    "suggestion": {
        "keywords": [],
        "cities": []
    },
    "districts": [
        {
            "citycode": "0571",
            "adcode": "330100",
            "name": "杭州市",
            "center": "120.210792,30.246026",
            "level": "city",
            "districts": [
                {
                    "citycode": "0571",
                    "adcode": "330102",
                    "name": "上城区",
                    "center": "120.19732,30.226543",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330127",
                    "name": "淳安县",
                    "center": "119.042015,29.609678",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330105",
                    "name": "拱墅区",
                    "center": "120.141503,30.319126",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330108",
                    "name": "滨江区",
                    "center": "120.211981,30.208332",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330114",
                    "name": "钱塘区",
                    "center": "120.493941,30.32304",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330182",
                    "name": "建德市",
                    "center": "119.281195,29.474964",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330112",
                    "name": "临安区",
                    "center": "119.724457,30.234375",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330113",
                    "name": "临平区",
                    "center": "120.299222,30.419154",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330110",
                    "name": "余杭区",
                    "center": "119.978742,30.273705",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330106",
                    "name": "西湖区",
                    "center": "120.130396,30.259242",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330109",
                    "name": "萧山区",
                    "center": "120.264263,30.184119",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330111",
                    "name": "富阳区",
                    "center": "119.96022,30.048803",
                    "level": "district",
                    "districts": []
                },
                {
                    "citycode": "0571",
                    "adcode": "330122",
                    "name": "桐庐县",
                    "center": "119.691755,29.79418",
                    "level": "district",
                    "districts": []
                }
            ]
        }
    ]
}
```

### 任务2: 高德天气
- 用户说:“我在杭州，今天的天气是什么？或者杭州天气”

#### 请改造成 tool use
- 入参
  - 默认 city=330100 ，表示杭州行政编码：可以借助 任务1: 高德行政区域查询的返回值 adcode 获取行政编码
  - key 是我的高德 api 
  - 调用方式
```curl
 curl --location 'https://restapi.amap.com/v3/weather/weatherInfo?city=330100&key=fa21eddea481b788af3458dc509364b2' \
--header 'Content-Type: application/json'
```
- 出参

| 名称 | | | 含义 | 规则说明 |
| :--- | --- | --- | :--- | :--- |
| status | | | 返回状态 | 值为0或1<br>1：成功；0：失败 |
| count | | | 返回结果总数目 | |
| info | | | 返回的状态信息 | |
| infocode | | | 返回状态说明,10000代表正确 | |
| lives | | | 实况天气数据信息 | |
| | province | | 省份名 | |
| | city | | 城市名 | |
| | adcode | | 区域编码 | |
| | weather | | 天气现象（汉字描述） | |
| | temperature | | 实时气温，单位：摄氏度 | |
| | winddirection | | 风向描述 | |
| | windpower | | 风力级别，单位：级 | |
| | humidity | | 空气湿度 | |
| | reporttime | | 数据发布的时间 | |

json demo
```json
{
    "status": "1",
    "count": "1",
    "info": "OK",
    "infocode": "10000",
    "lives": [
        {
            "province": "浙江",
            "city": "杭州市",
            "adcode": "330100",
            "weather": "阴",
            "temperature": "27",
            "winddirection": "南",
            "windpower": "≤3",
            "humidity": "62",
            "reporttime": "2025-04-19 16:32:20",
            "temperature_float": "27.0",
            "humidity_float": "62.0"
        }
    ]
}
```