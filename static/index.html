<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行政区划与天气查询助手</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #1890ff;
            color: white;
            padding: 20px 0;
            text-align: center;
            border-radius: 8px 8px 0 0;
            margin-bottom: 20px;
        }
        h1 {
            margin: 0;
            font-size: 24px;
        }
        .chat-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        .message {
            margin-bottom: 15px;
            max-width: 80%;
            padding: 10px 15px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }
        .user-message {
            align-self: flex-end;
            background-color: #1890ff;
            color: white;
            border-bottom-right-radius: 4px;
        }
        .assistant-message {
            align-self: flex-start;
            background-color: #f0f0f0;
            color: #333;
            border-bottom-left-radius: 4px;
        }
        .chat-input {
            display: flex;
            padding: 15px;
            border-top: 1px solid #eee;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            outline: none;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 20px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #bfbfbf;
            cursor: not-allowed;
        }
        .examples {
            margin-top: 20px;
        }
        .example-queries {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .example-query {
            background-color: #f0f0f0;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .example-query:hover {
            background-color: #e0e0e0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>行政区划与天气查询助手</h1>
        </header>
        
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <div class="message assistant-message">
                    您好！我是您的行政区划与天气查询助手。您可以询问我关于某个城市的行政区划或天气情况，例如“深圳的行政区划是什么”或“北京今天的天气怎么样”。
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="user-input" placeholder="输入您的问题..." />
                <button id="send-button">发送</button>
            </div>
        </div>
        
        <div class="examples">
            <h3>示例问题：</h3>
            <div class="example-queries">
                <div class="example-query">杭州的行政区划是什么？</div>
                <div class="example-query">我在杭州，今天的天气怎么样？</div>
                <div class="example-query">广州市有哪些区？</div>
                <div class="example-query">上海今天天气如何？</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatMessages = document.getElementById('chat-messages');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const exampleQueries = document.querySelectorAll('.example-query');
            
            // 添加示例问题点击事件
            exampleQueries.forEach(query => {
                query.addEventListener('click', function() {
                    userInput.value = this.textContent;
                    userInput.focus();
                });
            });
            
            // 发送消息函数
            function sendMessage() {
                const message = userInput.value.trim();
                if (!message) return;
                
                // 添加用户消息到聊天窗口
                addMessage(message, 'user');
                
                // 清空输入框
                userInput.value = '';
                
                // 禁用发送按钮
                sendButton.disabled = true;
                sendButton.innerHTML = '<span class="loading"></span>';
                
                // 发送请求到后端
                fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query: message })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // 添加助手响应
                    addMessage(data.response, 'assistant');
                })
                .catch(error => {
                    console.error('Error:', error);
                    addMessage('抱歉，处理您的请求时出现错误。请稍后再试。', 'assistant');
                })
                .finally(() => {
                    // 重新启用发送按钮
                    sendButton.disabled = false;
                    sendButton.textContent = '发送';
                });
            }
            
            // 添加消息到聊天窗口
            function addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message');
                messageDiv.classList.add(sender + '-message');
                messageDiv.textContent = text;
                
                chatMessages.appendChild(messageDiv);
                
                // 滚动到底部
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            // 点击发送按钮事件
            sendButton.addEventListener('click', sendMessage);
            
            // 回车键发送消息
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // 初始化时聚焦输入框
            userInput.focus();
        });
    </script>
</body>
</html>
