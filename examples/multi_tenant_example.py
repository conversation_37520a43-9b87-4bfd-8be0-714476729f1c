"""
多租户连接器使用示例 - 演示如何在临床研究助手中使用多租户功能
"""
import os
import sys
import logging
from typing import Dict, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.executor import GenericToolExecutor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def main():
    """多租户连接器使用示例主函数"""

    # 1. 初始化工具执行器（使用多租户工具配置）
    config_path = os.path.abspath(os.path.join(
        os.path.dirname(__file__),
        '../config/multi_tenant_tool_example.json'
    ))
    executor = GenericToolExecutor(config_path)

    # 2. 加载租户配置
    tenant_config_path = os.path.abspath(os.path.join(
        os.path.dirname(__file__),
        '../config/tenant_configs.json'
    ))
    executor.connection_manager.load_tenant_configs_from_file(tenant_config_path)

    # 3. 设置示例API密钥（实际应用中应从环境变量获取）
    os.environ['RESEARCH_A_API_KEY'] = 'test_api_key_a'
    os.environ['RESEARCH_B_API_KEY'] = 'test_api_key_b'
    os.environ['RESEARCH_C_API_KEY'] = 'test_api_key_c'

    # 4. 为不同租户执行相同工具
    execute_for_tenant(executor, "tenant1", "query_clinical_protocol", {
        "protocol_id": "PROTOCOL-001",
        "tenant_id": "tenant1"
    })

    execute_for_tenant(executor, "tenant2", "query_clinical_protocol", {
        "protocol_id": "PROTOCOL-002",
        "tenant_id": "tenant2"
    })

    execute_for_tenant(executor, "tenant3", "query_visit_schedule", {
        "protocol_id": "PROTOCOL-003",
        "subject_id": "SUBJECT-001",
        "tenant_id": "tenant3"
    })


def execute_for_tenant(
    executor: GenericToolExecutor,
    tenant_id: str,
    tool_name: str,
    arguments: Dict[str, Any]
) -> None:
    """
    为指定租户执行工具

    Args:
        executor: 工具执行器
        tenant_id: 租户ID
        tool_name: 工具名称
        arguments: 工具参数
    """
    tenant_config = executor.connection_manager.get_tenant_config(tenant_id)
    if not tenant_config:
        logger.error(f"未找到租户配置: {tenant_id}")
        return

    logger.info(f"为租户 '{tenant_config.get('name')}' ({tenant_id}) 执行工具 '{tool_name}'")
    logger.info(f"使用API基础URL: {tenant_config.get('api_base_url')}")
    logger.info(f"使用连接ID: {tenant_config.get('connection_id')}")

    # 注意：在实际应用中，这里会发送真实的API请求
    # 由于这是示例，我们只打印调用信息
    logger.info(f"工具参数: {arguments}")
    logger.info("这是示例脚本，不会发送实际API请求")
    logger.info("-" * 50)


if __name__ == "__main__":
    main()
