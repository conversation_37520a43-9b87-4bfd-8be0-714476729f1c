# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_BASE=https://openrouter.ai/api/v1
LLM_MODEL_NAME=anthropic/claude-3-opus:beta

# Milvus Configuration
MILVUS_USE_REMOTE=false
MILVUS_COLLECTION_NAME=icd10_knowledge
MILVUS_LITE_FILE=./milvus_lite_data.db
# Only needed if MILVUS_USE_REMOTE=true
MILVUS_REMOTE_URI=your_milvus_uri_here
MILVUS_REMOTE_TOKEN=your_milvus_token_here
MILVUS_INSERT_BATCH_SIZE=50

# Embedding Configuration
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
USE_HF_REMOTE_EMBEDDING=false
HF_API_TOKEN=your_huggingface_token_here
EMBEDDING_DEVICE=cpu
NORMALIZE_EMBEDDINGS=true

# Data Configuration
INPUT_CSV_PATH=./data/icd10cm_codes_2025.csv
PROCESSING_BATCH_SIZE=100

# Prompt Configuration
PROMPT_FILE_PATH=./prompts/prompt_en.md
QUERY_PROMPT_FILE_PATH=./prompts/query_prompt.md