import unittest
from app.utils.text_locator import find_start_position, Position

# --- Unit Tests ---

class TestFindStartPosition(unittest.TestCase):

    def test_simple_match(self):
        text = "患者入院初步评估：体温36.5℃，脉搏110次/分，吸气18次/分。病情观察6小时后记录：体温36.7℃，脉搏112次/分，吸气28次/分，血压97/70mmHg"
        context = "脉搏110次/分，吸气"
        value = "110次/分"
        self.assertEqual(find_start_position(text, context, value), Position(start=19, end=25))

    def test_context_at_beginning(self):
        text = "体温36.5℃，脉搏110次/分，吸气18次/分。"
        context = "体温36.5℃，脉搏"
        value = "36.5℃"
        self.assertEqual(find_start_position(text, context, value), Position(start=2, end=7))

    def test_context_at_end(self):
        text = "吸气18次/分。病情观察6小时"
        context = "吸气18次/分。病情观察6"
        value = "18次/分"
        self.assertEqual(find_start_position(text, context, value), Position(start=2, end=7))

    def test_no_match_context(self):
        text = "患者入院初步评估：体温36.5℃"
        context = "脉搏110次/分"
        value = "110次/分"
        self.assertIsNone(find_start_position(text, context, value))

    def test_no_match_value_in_found_context(self):
        # Context 存在，但 context 内部的 value 和我们要找的不一样
        text = "体温36.5℃，脉搏110次/分，吸气18次/分。其他：脉搏110次/分。"
        context = "脉搏110次/分。" # 这个 context 匹配最后一个
        value = "110次/分" # 但我们期望的是第一个脉搏的上下文
        # 假设正确的上下文是 "℃，脉搏110次/分，吸气"
        correct_context = "℃，脉搏110次/分，吸气"
        self.assertEqual(find_start_position(text, correct_context, value), Position(start=10, end=16))
        # 新逻辑：即使 context 匹配两次 ("脉搏110次/分。" at index 10 and 31),
        # 第一次匹配 (index 10) 无法在内部定位 value "110次/分" (text[12:18] != value)
        # 第二次匹配 (index 31) 可以定位 value (text[33:39] == value)
        # 因此 find_start_position 会找到一个有效位置并返回
        self.assertEqual(find_start_position(text, context, value), Position(start=30, end=36))

    def test_multiple_context_matches_return_first(self): # Renamed test
        text = "A B C D. A B C E."
        context = "A B C"
        value = "B"
        # 完整 context "A B C" 匹配两次 (index 0 and 8), 都能定位 value "B".
        # 新逻辑：返回第一个匹配的位置。
        self.assertEqual(find_start_position(text, context, value), Position(start=2, end=3))

    def test_shrinking_context_success_unique(self):
        text = "X Y Z W. P Y Z Q."
        context = "X Y Z W" # 完整上下文只匹配第一个
        value = "Y Z"
        self.assertEqual(find_start_position(text, context, value), Position(start=2, end=5))

        # --- 测试文档中的跨段落例子 ---
        # 模拟一个稍微复杂点，完整上下文找不到，需要缩小
        # text2 = "start prefix Y Z suffix end. other prefix Y Z suffix ..."
        # context2 = "prefix Y Z suffix" # 假设这个完整 context 在 text2 中不唯一或不存在
        # value2 = "Y Z"
        # 模拟缩小后的唯一上下文
        # shrunk_context = "prefix Y Z"
        # 假设 text2 中 "prefix Y Z" 只出现一次
        # text_mock_shrink = "unique prefix Y Z suffix end."
        # 注意：这个测试用例设计需要调整，因为函数逻辑是先找 context2，找不到才缩小
        # 如果 context2 找不到，缩小到 shrunk_context 能在 text_mock_shrink 中找到
        # 需要构造一个 text，使得 context2 找不到，但 shrunk_context 能找到
        # text_for_shrink = "some prefix Y Z suffix here. unique shrunk Y Z context there."
        # context_for_shrink = "prefix Y Z suffix" # 假设这个找不到或不唯一
        # value_for_shrink = "Y Z"
        # 期望结果是 "unique shrunk Y Z context there." 中 Y Z 的位置
        # 这个测试用例设计有点复杂，先用文档的例子
        para1 = "患者入院初步评估：体温36.5℃，脉搏110次/分，吸气18次/分"
        cross_context = "吸气18次/分。病情观察6" # 这个在 para1 中找不到
        value_cross = "18次/分"
        # 缩小到 "吸气18次/分" 能找到
        self.assertEqual(find_start_position(para1, cross_context, value_cross), Position(start=28, end=33))


    def test_shrinking_context_returns_first_ambiguous(self): # Renamed test
         text = "prefix Y Z suffix. another prefix Y Z suffix."
         context = "prefix Y Z suffix" # 完整上下文匹配两次 (index 0 and 20)
         value = "Y Z"
         # 新逻辑：在完整上下文匹配阶段就找到两个匹配，直接返回第一个。
         # 不会进入缩小上下文的逻辑。
         self.assertEqual(find_start_position(text, context, value), Position(start=7, end=10))

    def test_debug(self): # Renamed test
         text =   "患者术后返回病房，即刻生命体征监测：体温36.6℃，呼吸频率16次/分，**心率** 95次/分（心电监护示），血压袖带自动测量：**收缩期压力** 115mmHg，**舒张期压力** 70mmHg，指脉氧饱和度97%（鼻导管吸氧2L/min）。患者麻醉未完全清醒，对呼唤有反应。 术后4小时复测：体温37.0℃，呼吸18次/分，触诊**桡动脉搏动** 88次/分，节律规整，血压手动测量为 **120**/**78** mmHg（右上臂）。指脉氧饱和度98%（已停吸氧）。患者神志清楚，切口疼痛评分3分（VAS评分）。"
         context = "120/**78** mmHg"
         value = "78"
         p = find_start_position(text, context, value)
         print("--------------------->")
         print(p)

    def test_value_not_in_context_input(self):
        text = "some text here"
        context = "some text"
        value = "not here"
        self.assertIsNone(find_start_position(text, context, value))

    def test_empty_inputs(self):
        self.assertIsNone(find_start_position("", "ctx", "val"))
        self.assertIsNone(find_start_position("text", "", "val"))
        self.assertIsNone(find_start_position("text", "ctx", ""))

    def test_special_chars_in_context(self):
        text = "Value is $10.00? Yes."
        context = "$10.00? Yes"
        value = "$10.00"
        # 注意：原始文本中 $ 在第 9 位 (0-based index)
        # 结束位置是 9 + len("$10.00") = 9 + 6 = 15
        self.assertEqual(find_start_position(text, context, value), Position(start=9, end=15))

    def test_cross_paragraph_example_from_doc_handled_by_caller(self):
        # 根据澄清，函数只处理单一段落
        para1 = "患者入院初步评估：体温36.5℃，脉搏110次/分，吸气18次/分"
        para2 = "病情观察6小时后记录：体温36.7℃，脉搏112次/分，吸气28次/分，血压97/70mmHg"

        # 跨段落的上下文
        cross_context = "吸气18次/分。病情观察6"
        value = "18次/分"

        # 在第一段查找，完整上下文找不到，缩小上下文
        # 缩小到 "吸气18次/分" 可以在第一段找到
        self.assertEqual(find_start_position(para1, cross_context, value), Position(start=28, end=33))

        # 在第二段查找，找不到
        self.assertIsNone(find_start_position(para2, cross_context, value))


if __name__ == '__main__':
    unittest.main(argv=['first-arg-is-ignored'], exit=False)