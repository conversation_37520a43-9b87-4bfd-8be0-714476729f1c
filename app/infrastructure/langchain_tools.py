"""
Langchain 工具包装模块 - 将通用工具执行器包装为 Langchain 工具
"""
import logging
from typing import Dict, Any, Type, Optional, List, Union

from langchain_core.tools import BaseTool
from pydantic.v1 import BaseModel, Field, create_model # Use v1 for compatibility

logger = logging.getLogger(__name__)


def map_openapi_type_to_python(openapi_type: str) -> Type:
    """将OpenAPI数据类型映射到Python类型"""
    mapping = {
        "string": str,
        "integer": int,
        "number": float,
        "boolean": bool,
        # 可以根据需要添加更多类型，例如 'array', 'object'
    }
    return mapping.get(openapi_type, str) # 默认为字符串


def create_dynamic_args_schema(tool_name: str, tool_config: Dict[str, Any]) -> Type[BaseModel]:
    """根据工具配置动态创建Pydantic参数模型 (支持 OpenAPI 或直接 input_schema)"""
    fields: Dict[str, Any] = {}
    openapi_spec = tool_config.get("openapi")

    if openapi_spec:
        # 假设只有一个 path 和 method
        path_item_key = next(iter(openapi_spec.get("paths", {})), None)
        if path_item_key:
            path_item = openapi_spec["paths"][path_item_key]
            method = next(iter(path_item), None)
            if method:
                operation = path_item[method]

                # 1. 处理 parameters (query, path, header)
                for param_def in operation.get("parameters", []):
                    param_name = param_def.get("name")
                    param_schema = param_def.get("schema", {})
                    param_type = param_schema.get("type", "string")
                    python_type = map_openapi_type_to_python(param_type)
                    description = param_def.get("description", "")
                    is_required = param_def.get("required", False)
                    is_api_key = param_def.get("x-is-api-key", False)

                    # API Key 通常由系统处理，不在用户输入参数中
                    if param_name and not is_api_key:
                        if is_required:
                            fields[param_name] = (python_type, Field(..., description=description))
                        else:
                            # 对于非必需参数，提供一个默认值 None
                            fields[param_name] = (Optional[python_type], Field(default=None, description=description))

                # 2. 处理 requestBody (假设 content-type 是 application/json)
                request_body = operation.get("requestBody")
                if request_body:
                    content = request_body.get("content", {})
                    json_schema = content.get("application/json", {}).get("schema", {})

                    if json_schema.get("type") == "object" and "properties" in json_schema:
                        required_body_params = set(json_schema.get("required", []))
                        for prop_name, prop_def in json_schema.get("properties", {}).items():
                            prop_type = prop_def.get("type", "string")
                            python_type = map_openapi_type_to_python(prop_type)
                            description = prop_def.get("description", "")
                            is_required = prop_name in required_body_params

                            if is_required:
                                fields[prop_name] = (python_type, Field(..., description=description))
                            else:
                                fields[prop_name] = (Optional[python_type], Field(default=None, description=description))
    else:
        # Handle direct 'input_schema' if 'openapi' is not present
        input_schema = tool_config.get("input_schema")
        if input_schema and input_schema.get("type") == "object" and "properties" in input_schema:
            logger.debug(f"工具 '{tool_name}': Using 'input_schema' for Pydantic model generation.")
            required_params = set(input_schema.get("required", []))
            for prop_name, prop_def in input_schema.get("properties", {}).items():
                prop_type = prop_def.get("type", "string") # Default to string if type missing
                python_type = map_openapi_type_to_python(prop_type)
                description = prop_def.get("description", "")
                default_value = prop_def.get("default") # Get default if specified
                is_required = prop_name in required_params

                # Note: Pydantic v1 uses '...' for required fields without defaults.
                # If a field is required but HAS a default in the schema, Pydantic treats it as optional with a default.
                if is_required:
                    # If required and has default, Field definition includes default. If no default, use ...
                    if default_value is not None:
                         fields[prop_name] = (python_type, Field(default=default_value, description=description))
                    else:
                         fields[prop_name] = (python_type, Field(..., description=description))
                else:
                    # Optional field: Use Optional[type] and provide default
                    fields[prop_name] = (Optional[python_type], Field(default=default_value, description=description))
        elif input_schema and input_schema.get("type") == "object" and not input_schema.get("properties"):
             # Handle cases like get_current_time where properties is empty/null
             logger.debug(f"工具 '{tool_name}': 'input_schema' is object with no properties. Creating empty model.")
             # No fields to add, will result in an empty model later
             pass
        else:
             logger.warning(f"工具 '{tool_name}': Missing or invalid 'openapi' spec and 'input_schema'. Cannot generate specific args model.")
             # No fields to add, will result in an empty model later
             pass

    # 如果没有解析到任何字段，创建一个空的模型
    if not fields:
        logger.warning(f"无法为工具 '{tool_name}' 生成参数模型，将使用空模型。")
        return create_model(f"{tool_name.capitalize()}Args", __base__=BaseModel)

    # 创建 Pydantic 模型
    sanitized_tool_name = ''.join(filter(str.isalnum, tool_name.replace('_', ' ').title().replace(' ', '')))
    model_name = f"{sanitized_tool_name}Args"
    try:
        args_schema = create_model(model_name, **fields, __base__=BaseModel)
        logger.debug(f"Successfully created args model '{model_name}' for tool '{tool_name}'.")
        return args_schema
    except Exception as e:
        logger.error(f"创建 Pydantic 模型 '{model_name}' for tool '{tool_name}' 时出错: {e}", exc_info=True)
        # Fallback to empty model on creation error
        return create_model(f"{tool_name.capitalize()}Args", __base__=BaseModel)


class LangchainToolWrapper(BaseTool):
    """将我们的通用工具执行器包装成Langchain工具"""
    executor: Any # 使用 Any 避免直接导入 Executor，它会在运行时传入
    tool_name: str

    def __init__(self, executor: Any, tool_name: str, tool_config: Dict[str, Any]):
        """
        初始化工具包装器

        Args:
            executor: GenericToolExecutor 的实例
            tool_name: 工具名称
            tool_config: 工具配置字典
        """
        name = tool_name
        description = tool_config.get("description", "")
        # 动态创建参数模型
        args_schema = create_dynamic_args_schema(name, tool_config)

        # 调用父类初始化
        super().__init__(
            name=name,
            description=description,
            args_schema=args_schema,
            executor=executor, # Pydantic v1 可能不允许未声明的字段，但这里 executor 是声明的
            tool_name=tool_name # 确保 tool_name 也被正确赋值
        )

    def _run(self, **kwargs: Any) -> Union[Dict[str, Any], str]:
        """同步执行工具"""
        logger.info(f"LangchainToolWrapper: Running tool '{self.tool_name}' with args: {kwargs}")
        result = self.executor.execute(tool_name=self.tool_name, arguments=kwargs)
        
        # 检查执行结果
        if isinstance(result, dict):
            if result.get("code") == 0:
                # 成功: 返回 data 部分 (应该是字典)
                data = result.get("data")
                if isinstance(data, dict):
                    return data
                else:
                    # 如果 data 不是字典，也作为字符串返回（异常情况）
                    logger.warning(f"Tool '{self.tool_name}' succeeded but 'data' is not a dict: {type(data)}")
                    return str(data)
            else:
                # 失败: 返回错误信息字符串
                error_message = result.get("message", "Unknown error executing tool")
                logger.error(f"Tool '{self.tool_name}' failed: Code {result.get('code')}, Message: {error_message}")
                return f"Error executing tool {self.tool_name}: {error_message}"
        else:
            # 如果返回的不是标准字典，直接返回字符串表示
            logger.warning(f"Tool '{self.tool_name}' returned unexpected type: {type(result)}. Converting to string.")
            return str(result)

    async def _arun(self, **kwargs: Any) -> str:
        """异步执行工具 (如果执行器支持异步)"""
        # 当前 GenericToolExecutor.execute 是同步的，所以我们直接调用 _run
        # 如果 executor 支持异步执行，这里需要调用异步方法
        logger.info(f"LangchainToolWrapper: Running tool '{self.tool_name}' asynchronously with args: {kwargs}")
        return self._run(**kwargs)


def create_langchain_tools(executor: Any) -> List[BaseTool]:
    """根据执行器中的配置创建所有Langchain工具"""
    tools = []
    all_tool_configs = executor.tools.values()
    for tool_config in all_tool_configs:
        tool_name = tool_config.get("name")
        try:
            langchain_tool = LangchainToolWrapper(executor=executor, tool_name=tool_name, tool_config=tool_config)
            tools.append(langchain_tool)
            logger.info(f"Successfully created Langchain tool wrapper for '{tool_name}'")
        except Exception as e:
            logger.error(f"Failed to create Langchain tool wrapper for '{tool_name}': {e}")
    return tools
