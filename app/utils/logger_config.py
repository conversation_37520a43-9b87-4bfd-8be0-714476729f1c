"""
统一日志配置模块
提供集中化的日志管理，支持文件输出、轮转和格式化
"""
import logging
import logging.handlers
import os
from pathlib import Path


def setup_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    log_filename: str = "api.log",
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_output: bool = True
):
    """
    设置统一的日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: 日志目录路径
        log_filename: 日志文件名
        max_bytes: 单个日志文件最大大小（字节）
        backup_count: 保留的备份文件数量
        console_output: 是否同时输出到控制台
    """
    # 确保日志目录存在
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # 完整的日志文件路径
    log_file_path = log_path / log_filename
    
    # 设置日志级别
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # 清除现有的处理器，避免重复
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 定义日志格式
    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 创建文件处理器（支持轮转）
    file_handler = logging.handlers.RotatingFileHandler(
        filename=str(log_file_path),
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(numeric_level)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # 创建控制台处理器（可选）
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 记录日志系统初始化信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已初始化，日志文件: {log_file_path.absolute()}")
    logger.info(f"日志级别: {log_level}, 最大文件大小: {max_bytes/1024/1024:.1f}MB, 备份数量: {backup_count}")
    
    return str(log_file_path.absolute())


def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志记录器实例
    
    Args:
        name: 日志记录器名称，通常使用 __name__
        
    Returns:
        配置好的日志记录器实例
    """
    return logging.getLogger(name)


# 环境变量配置支持
def setup_logging_from_env():
    """
    从环境变量读取配置并设置日志系统
    支持的环境变量：
    - LOG_LEVEL: 日志级别 (默认: INFO)
    - LOG_DIR: 日志目录 (默认: logs)
    - LOG_FILENAME: 日志文件名 (默认: api.log)
    - LOG_MAX_BYTES: 最大文件大小 (默认: 10485760, 即10MB)
    - LOG_BACKUP_COUNT: 备份文件数量 (默认: 5)
    - LOG_CONSOLE_OUTPUT: 是否输出到控制台 (默认: true)
    """
    log_level = os.getenv("LOG_LEVEL", "INFO")
    log_dir = os.getenv("LOG_DIR", "logs")
    log_filename = os.getenv("LOG_FILENAME", "api.log")
    max_bytes = int(os.getenv("LOG_MAX_BYTES", "10485760"))  # 10MB
    backup_count = int(os.getenv("LOG_BACKUP_COUNT", "5"))
    console_output = os.getenv("LOG_CONSOLE_OUTPUT", "true").lower() == "true"
    
    return setup_logging(
        log_level=log_level,
        log_dir=log_dir,
        log_filename=log_filename,
        max_bytes=max_bytes,
        backup_count=backup_count,
        console_output=console_output
    ) 