import os
from langchain_community.embeddings import HuggingFaceEmbeddings
import logging

logger = logging.getLogger(__name__)

# 初始化嵌入模型，提供默认值
DEFAULT_EMBEDDING_MODEL = "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"
# 本地模型目录路径
LOCAL_MODELS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "embedding_models")
_embedding_model = None  # 全局变量，用于缓存模型实例

def get_local_model_path(model_name):
    """
    根据模型名称获取本地模型路径
    如果本地存在模型，返回本地路径；否则返回原始模型名称
    """
    # 从模型名称中提取最后一部分作为目录名
    model_dir_name = model_name.split('/')[-1] if '/' in model_name else model_name
    local_model_path = os.path.join(LOCAL_MODELS_DIR, model_dir_name)

    # 检查本地模型目录是否存在
    if os.path.exists(local_model_path) and os.path.isdir(local_model_path):
        logger.info(f"找到本地模型: {local_model_path}")
        return local_model_path
    else:
        logger.warning(f"本地模型目录不存在: {local_model_path}，将尝试从网络加载模型")
        return model_name

def get_embedding_model():
    """获取嵌入模型实例，如果尚未加载则进行初始化。"""
    global _embedding_model
    if _embedding_model is None:
        embedding_model_name = os.getenv("EMBEDDING_MODEL_NAME")
        if not embedding_model_name:
            embedding_model_name = DEFAULT_EMBEDDING_MODEL
            logger.warning(f"EMBEDDING_MODEL_NAME环境变量未设置或为空。使用默认值: {DEFAULT_EMBEDDING_MODEL}")

        # 检查本地模型路径
        model_path = get_local_model_path(embedding_model_name)

        # try: # 注释掉外部的try块
            # 尝试使用GPU加载模型 - 注释掉，强制使用CPU
            # _embedding_model = HuggingFaceEmbeddings(
            #     model_name=model_path,
            #     model_kwargs={'device': 'cuda'}  # 指定使用 GPU
            #     # 可以根据需要添加其他参数，如 encode_kwargs={'normalize_embeddings': True}
            # )
            # logger.info(f"已初始化HuggingFaceEmbeddings模型: {model_path}，设备: cuda")
        # except Exception as e: # 注释掉GPU加载的异常捕获
            # logger.error(f"在GPU上初始化嵌入模型失败: {e}")
            # logger.info("回退到CPU加载嵌入模型。")
        try: # 直接尝试在 CPU 上加载
            _embedding_model = HuggingFaceEmbeddings(
                model_name=model_path
                # 尝试在 CPU 上加载
            )
            logger.info(f"已初始化HuggingFaceEmbeddings模型: {model_path}，设备: cpu")
        except Exception as inner_e:
            logger.critical(f"在CPU上初始化嵌入模型失败: {inner_e}")
            # 如果 CPU 也失败，可以考虑抛出异常或返回 None，取决于应用如何处理
            raise RuntimeError(f"无法在GPU或CPU上初始化嵌入模型: {inner_e}") from inner_e

    return _embedding_model

def get_embedding_dimension():
    """获取嵌入模型的向量维度"""
    model = get_embedding_model()
    # 使用一个简单的文本生成嵌入向量，然后获取其维度
    sample_text = "测试文本"
    embedding = model.embed_query(sample_text)
    # 返回向量维度
    dimension = len(embedding)
    logger.info(f"嵌入模型向量维度: {dimension}")
    return dimension
