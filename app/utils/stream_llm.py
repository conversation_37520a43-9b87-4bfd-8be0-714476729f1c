"""
流式调用 LLM API 的工具函数，用于获取思维链
"""
import os
import logging
from typing import Any
from openai import OpenAI
from tqdm import tqdm
from app.utils.openrouter_config import get_openrouter_extra_args, get_openrouter_max_tokens

logger = logging.getLogger(__name__)

def call_llm_api_stream(
        prompt: str,
        provider: str,
        model: str = None,
        temperature: float = 0.0,
        max_tokens: int = 8192
) -> Any:
    """
    以流式方式调用 LLM API，逐步 yield 内容片段，每次为 {'content': ..., 'thinkLogic': ...}
    每次chunk都尝试提取思维链
    """
    if provider.lower() == "openrouter":
        api_key = os.getenv("OPENROUTER_API_KEY")
        base_url = os.getenv("OPENROUTER_API_BASE")
        model = model or "deepseek/deepseek-r1"  # OpenRouter 的默认模型
        provider_name = "OpenRouter"
        
        # 使用新的配置工具函数获取额外参数
        extra_args = get_openrouter_extra_args()
        
        # 如果没有明确指定max_tokens，使用配置的值
        if max_tokens == 8192:  # 默认值
            max_tokens = get_openrouter_max_tokens()
            
        if not api_key:
            logger.error("缺少 OpenRouter API 配置，请检查环境变量 OPENROUTER_API_KEY")
            yield {"content": "API 配置错误，无法调用 LLM", "thinkLogic": None}
            return
    elif provider.lower() == "dashscope":
        api_key = os.getenv("DASHSCOPE_API_KEY")
        base_url = os.getenv("DASHSCOPE_API_BASE")
        model = model or "deepseek-r1"  # OpenRouter 的默认模型
        provider_name = "DashScope"
        extra_args = {}
    else:
        logger.error(f"不支持的 API 提供商: {provider}")
        yield {"content": f"不支持的 API 提供商: {provider}", "thinkLogic": None}
        return

    client = OpenAI(
        api_key=api_key,
        base_url=base_url,
        default_headers={
            "HTTP-Referer": "https://prime-rag-api.example.com",
            "X-Title": "Prime RAG API"
        }
    )

    try:
        logger.info(f"正在调用 {provider_name} API (模型: {model}) [流式]")
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            stream=True,
            **extra_args
        )
        with tqdm(desc="生成中", total=100, bar_format="{desc}: {n_fmt} chunks", ncols=10) as pbar:
            for chunk in response:
                # logger.info(f"chunk: ${chunk}")
                thinking_process = None
                message = getattr(chunk.choices[0], 'delta', None)
                pbar.update(1)
                
                # 1. 尝试从 message 中提取思维链
                for key in ["reasoning_content", "reasoning", "thinking"]:
                    if hasattr(message, key) and getattr(message, key):
                        thinking_process = getattr(message, key)
                        # logger.info(f"从 {provider_name} API 响应中提取到思维链 (字段: {key})")
                        break

                # 2. 如果直接属性不存在，尝试从 message.model_extra 中提取
                if not thinking_process and hasattr(message, 'model_extra') and message.model_extra:
                    for key in ["reasoning_content", "reasoning", "thinking"]:
                        if key in message.model_extra:
                            thinking_process = message.model_extra[key]
                            # logger.info(f"从 message.model_extra 中提取到思维链 (字段: {key})")
                            break

                content = None
                if message and hasattr(message, 'content') and getattr(message, 'content'):
                    content = getattr(message, 'content')
                # logger.info(f"thinking_process: {thinking_process}, content: {content}")
                if thinking_process or content:
                    yield {"content": content, "thinkLogic": thinking_process}
    except Exception as e:
        logger.error(f"调用 {provider_name} API 时发生错误: {e}")
        yield {"content": f"API 调用错误: {str(e)}", "thinkLogic": None}
