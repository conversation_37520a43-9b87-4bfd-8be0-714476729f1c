import pandas as pd
import re
import json 

# 映射回原始文本位置
def map_no_space_to_original(no_space_pos,original_D_txt):
    current_no_space_pos = 0
    for i, char in enumerate(original_D_txt):
        if current_no_space_pos == no_space_pos:
            return i
        if char != " ":
            current_no_space_pos += 1
    return len(original_D_txt)

def is_missing_spaces(hit_value, original_text):
    """判断hit_value是否缺失了空格"""
    # 去掉两边的空格，只比较中间内容
    hit_value_stripped = str(hit_value).strip()
    original_text_stripped = original_text.strip()
    
    # 计算去掉两边空格后的空格数量
    original_spaces = original_text_stripped.count(" ")
    hit_value_spaces = hit_value_stripped.count(" ")
    
    # 如果原文空格数量多于hit_value，说明缺失了空格
    return original_spaces > hit_value_spaces

def PostProcess_MissingSpaces(evidence: json, D_txt: str) -> tuple[list[str], list[int]]:
    """
    处理evidence中缺失空格的情况
    找到hit_value在原文中对应的起始位置
    """
    original_D_txt = D_txt
    D_txt_no_spaces = D_txt.replace(" ", "")
    
    original_evidences = []
    modified_evidences = []
    modified_evidence_indices = []
    
    for key, hit_value in evidence.items():
        if "hit_value" in key and hit_value:
            hit_value_no_spaces = str(hit_value).replace(" ", "")
            
            # 在无空格的D_txt中查找hit_value
            if hit_value_no_spaces in D_txt_no_spaces:
                hit_start_no_spaces = D_txt_no_spaces.find(hit_value_no_spaces)
                hit_end_no_spaces = hit_start_no_spaces + len(hit_value_no_spaces)
                
                original_start = map_no_space_to_original(hit_start_no_spaces,original_D_txt)
                original_end = map_no_space_to_original(hit_end_no_spaces,original_D_txt)
                
                # 提取原文中的hit_value内容
                original_hit_text = original_D_txt[original_start:original_end]
                
                # 检查是否真的缺失了空格
                if is_missing_spaces(hit_value, original_hit_text):
                    # print(f"发现缺失空格: {hit_value}")
                    # print(f"原文内容: {original_hit_text}")
                    
                    original_evidences.append(hit_value)
                    modified_evidences.append(original_hit_text)
                    modified_evidence_indices.append([original_start, original_end])
                    
                    # print(f"位置: [{original_start}, {original_end}]")
                    # print("-" * 30)
    
        # if "体格检查" in hit_value:
        #     break
    
    return original_evidences, modified_evidences, modified_evidence_indices

if __name__ == "__main__":
    df = pd.read_excel("溯源badcase_分析_20250616.xlsx")
    
    
    for i in range(len(df)):
        print(f"=== 处理第 {i + 1} 行数据 ===")
        D_txt = df.iloc[i]["D字段内容"]
        evidence = df.iloc[i]["API响应-答案（generation）"]
        evidence = json.loads(evidence)
        
        # 调用处理函数
        original_evidences, modified_evidences, modified_evidence_indices = PostProcess_MissingSpaces(evidence, D_txt)
        
        if len(modified_evidences) > 0:
            for i in range(len(modified_evidences)):
                print(f"original evidence: '{original_evidences[i]}'")
                print(f"modified evidence: '{modified_evidences[i]}'")
                print(f"evidence_index: {modified_evidence_indices[i]}")
                
                # 显示区别
                orig = original_evidences[i]
                modi = modified_evidences[i]
                # if orig != modi:
                    # print(f"区别分析:")
                    # print(f"  original空格数: {orig.count(' ')}")
                    # print(f"  modified空格数: {modi.count(' ')}")
                    
                    # 找出不同的字符位置
                    # min_len = min(len(orig), len(modi))
                    # for j in range(min_len):
                    #     if orig[j] != modi[j]:
                    #         # print(f"  位置{j}: original='{orig[j]}' vs modified='{modi[j]}'")
                    #         break
                    
                    # if len(orig) != len(modi):
                    #     print(f"  长度不同: original={len(orig)} vs modified={len(modi)}")
                        
                # print("="*50)
        else:
            print("没有找到缺失空格的内容")
        print("="*50)
        
        # 可以选择只处理第一条记录进行测试
        # break