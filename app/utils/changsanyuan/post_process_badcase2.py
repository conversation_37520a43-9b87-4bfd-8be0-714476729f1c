import pandas as pd
import re
import json 

def clean_text(text):
    """清理文本，只保留中文、英文、数字，返回每个子句构成的列表"""
    # 按标点符号分割文本
    sentences = re.split(r'[，。！？,.!?；;:：]', text)
    # 清理每个子句，只保留中文字符、英文字母、数字
    cleaned_sentences = []
    for sentence in sentences:
        if sentence.strip():
            cleaned = re.sub(r'[^\u4e00-\u9fff\w]', '', sentence)
            if cleaned:  # 只保留非空的句子
                cleaned_sentences.append(cleaned)
    return cleaned_sentences

def find_sentence_positions(text):
    """找到原文中每个句子的位置"""
    sentences = re.split(r'([，。！？,.!?；;:：])', text)
    positions = []
    current_pos = 0
    
    for i in range(0, len(sentences), 2):  # 跳过分隔符
        if i < len(sentences) and sentences[i].strip():
            sentence = sentences[i]
            start_pos = text.find(sentence, current_pos)
            end_pos = start_pos + len(sentence)
            positions.append([start_pos, end_pos, sentence.strip()])
            current_pos = end_pos
    
    return positions

def find_matching_pattern(hit_value_sentences, d_txt_sentences):
    """找到hit_value_sentences在d_txt_sentences中的匹配模式"""
    if not hit_value_sentences:
        return []
    
    # 查找最长公共子序列的匹配位置
    best_match = []
    best_score = 0
    
    # 尝试在D_txt中找到hit_value序列的最佳匹配
    for start_idx in range(len(d_txt_sentences)):
        current_match = []
        d_idx = start_idx
        
        for hit_sentence in hit_value_sentences:
            # 在剩余的D_txt中查找当前hit_sentence
            found = False
            for j in range(d_idx, len(d_txt_sentences)):
                if hit_sentence == d_txt_sentences[j]:
                    current_match.append(j)
                    d_idx = j + 1
                    found = True
                    break
            
            if not found:
                break
        
        # 评估匹配质量
        if len(current_match) == len(hit_value_sentences):
            score = len(current_match)
            if score > best_score:
                best_score = score
                best_match = current_match
    
    return best_match

def is_missing_middle_content(match_indices, hit_value_sentences, d_txt_sentences):
    """判断是否缺失了中间内容"""
    if not match_indices or len(match_indices) < 2:
        return False
    
    # 检查匹配的索引是否连续
    for i in range(1, len(match_indices)):
        if match_indices[i] - match_indices[i-1] > 1:
            return True  # 发现不连续，说明缺失了中间内容
    
    # 检查匹配范围内的句子数量是否大于hit_value的句子数量
    start_idx = match_indices[0]
    end_idx = match_indices[-1]
    range_length = end_idx - start_idx + 1
    
    if range_length > len(hit_value_sentences):
        return True  # 范围内句子数量更多，说明有缺失
    
    return False

def PostProcess_MissingMiddleContent(evidence: json, D_txt: str) -> tuple[list[str], list[int]]:
    """
    处理evidence中缺失中间内容的情况
    找到hit_value在原文中对应的起始位置，并提取原文中的hit_value内容
    """
    original_D_txt = D_txt
    
    original_evidences = []
    modified_evidences = []
    modified_evidence_indices = []
    
    D_txt_sub_sentences = clean_text(D_txt)
    sentence_positions = find_sentence_positions(D_txt)
    
    # print(f"D_txt句子数量: {len(D_txt_sub_sentences)}")
    # print(f"D_txt清理后句子: {D_txt_sub_sentences}")
    
    for key, hit_value in evidence.items():
        # hit_value = "外耳道无异常分泌物，听力粗测正常"
        if "hit_value" in key and hit_value: #and hit_value == "外耳道无异常分泌物，听力粗测正常":
            hit_value_sub_sentences = clean_text(hit_value)
            # print(f"hit_value_sub_sentences: {hit_value_sub_sentences}")
            
            # 找到匹配模式
            match_indices = find_matching_pattern(hit_value_sub_sentences, D_txt_sub_sentences)
            # print(f"匹配的句子索引: {match_indices}")
            
            if match_indices:
                # 检查是否真的缺失了中间内容
                if is_missing_middle_content(match_indices, hit_value_sub_sentences, D_txt_sub_sentences):
                    # 获取完整的句子范围（包括缺失的中间句子）
                    start_idx = match_indices[0]
                    end_idx = match_indices[-1]
                    
                    print(f"发现缺失内容: {hit_value}")
                    print(f"匹配索引: {match_indices}")
                    print(f"原文句子范围: {start_idx} - {end_idx}")
                    
                    # 获取原文位置
                    if start_idx < len(sentence_positions) and end_idx < len(sentence_positions):
                        original_start = sentence_positions[start_idx][0]
                        original_end = sentence_positions[end_idx][1]
                        
                        original_evidences.append(hit_value)
                        # 提取完整的原文内容（包括缺失的部分）
                        complete_text = original_D_txt[original_start:original_end]
                        
                        modified_evidences.append(complete_text)
                        modified_evidence_indices.append([original_start, original_end])
                        
                        print(f"补齐后的完整内容: {complete_text}")
                        print(f"位置: [{original_start}, {original_end}]")
                        print("-" * 30)
            
            # break
    
    return original_evidences, modified_evidences, modified_evidence_indices

if __name__ == "__main__":
    df = pd.read_excel("溯源badcase_分析_20250616.xlsx")
    for i in range(len(df)):
        print(f"=== 处理第 {i + 1} 行数据 ===")
        D_txt = df.iloc[i]["D字段内容"]
        evidence = df.iloc[i]["API响应-答案（generation）"]
        evidence = json.loads(evidence)
    
        # 调用处理函数
        original_evidences, modified_evidences, modified_evidence_indices = PostProcess_MissingMiddleContent(evidence, D_txt)
        if len(original_evidences) > 0:
            for i in range(len(modified_evidences)):
                print(f"hit_value: {original_evidences[i]}")
                print(f"modified evidence: {modified_evidences[i]}")
                print(f"evidence_index: {modified_evidence_indices[i]}")
                print("="*50)
        else:
            print("没有找到缺失内容")