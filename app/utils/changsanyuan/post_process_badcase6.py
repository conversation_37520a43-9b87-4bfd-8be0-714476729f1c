# -*- coding: utf-8 -*-
import re
import ast
import json
import time

import pandas as pd
from loguru import logger


def split_text_into_clauses(text):
    """
    将文本分割为子句，分隔符包括标点符号和省略号
    """
    # 预处理：在日期和标点符号之间添加分隔符，便于分割出纯净的子句
    # 匹配形如 YYYY-MM-DD 的日期格式，并在日期后的文字前加标点
    date_pattern = r'(\d{4}[-/]?\d{1,2}[-/]?\d{1,2})([^\d\s，。！？,.!?；;:：])'
    text = re.sub(date_pattern, r'\1，\2', text)

    # 扩展分隔符，包括省略号
    separators = r'[，。！？,.!?；;:：]|\.\.\.'
    clauses = re.split(separators, text)

    cleaned_clauses = []
    for clause in clauses:
        clause = clause.strip()
        if clause:  # 只保留非空子句
            # 进一步清理：如果子句是纯日期，跳过
            if not re.match(r'^\d{4}[-/]?\d{1,2}[-/]?\d{1,2}$', clause):
                cleaned_clauses.append(clause)

    return cleaned_clauses


def clean_text_for_matching(text):
    """清理文本用于匹配，只保留中文、英文、数字"""
    return re.sub(r'[^\u4e00-\u9fff\w]', '', text)


def find_clause_positions_in_original(text):
    """找到原文中每个子句的位置"""
    # 扩展分隔符模式，包括省略号
    separators_pattern = r'([，。！？,.!?；;:：]|\.\.\.)'
    parts = re.split(separators_pattern, text)

    positions = []
    current_pos = 0

    for i in range(0, len(parts), 2):  # 跳过分隔符
        if i < len(parts) and parts[i].strip():
            clause = parts[i]
            start_pos = text.find(clause, current_pos)
            if start_pos != -1:
                end_pos = start_pos + len(clause)
                positions.append([start_pos, end_pos, clause.strip()])
                current_pos = end_pos

    return positions


def find_best_clause_sequence_match(hit_clauses, original_clauses, original_hit_value=""):
    """
    找到hit_clauses在original_clauses中的最佳匹配序列
    更严格的匹配：只进行精确匹配，不进行部分匹配
    只处理真正的缺失内容情况（省略号...或明确的中间缺失）
    """
    if not hit_clauses:
        return []

    # 清理子句用于匹配
    clean_hit_clauses = [clean_text_for_matching(clause) for clause in hit_clauses]
    clean_original_clauses = [clean_text_for_matching(clause) for clause in original_clauses]

    # 检查原始hit_value是否包含省略号
    has_ellipsis = '...' in original_hit_value

    best_match = []
    best_score = 0

    # 尝试不同的起始位置
    for start_idx in range(len(clean_original_clauses)):
        current_match = []
        orig_idx = start_idx
        matched_hit_count = 0

        for i, hit_clause in enumerate(clean_hit_clauses):
            if not hit_clause:  # 跳过空子句
                continue

            found = False
            # 首先尝试精确匹配
            for j in range(orig_idx, len(clean_original_clauses)):
                if hit_clause == clean_original_clauses[j]:
                    current_match.append(j)
                    orig_idx = j + 1
                    matched_hit_count += 1
                    found = True
                    break

            if not found:
                break

        # 严格的评估标准：必须匹配所有子句
        if matched_hit_count >= 2 and matched_hit_count == len([c for c in clean_hit_clauses if c]):

            # 检查是否有间隔（表示有缺失内容）
            has_gaps = False
            for k in range(1, len(current_match)):
                if current_match[k] - current_match[k - 1] > 1:
                    has_gaps = True
                    break

            # 只有确实有间隔或者包含省略号的情况才处理
            if has_gaps or has_ellipsis:
                score = matched_hit_count * 10 + (10 if has_gaps else 0)
                if score > best_score:
                    best_score = score
                    best_match = current_match

    return best_match


def has_missing_content_between_clauses(match_indices):
    """判断匹配的子句之间是否有缺失的内容"""
    if not match_indices or len(match_indices) < 2:
        return False

    # 检查匹配的索引是否连续
    for i in range(1, len(match_indices)):
        if match_indices[i] - match_indices[i - 1] > 1:
            return True  # 发现不连续，说明缺失了中间内容

    return False


def find_smart_end_position(hit_value, d_txt):
    """
    智能查找结束位置，基于hit_value的最后有意义内容
    """
    # 从hit_value中提取最后的有意义内容
    # 例如：从"病理分期：pT2N1bM1，考虑为IV期肺内病灶远处转移的全身性治疗。"
    # 直接在原文中搜索这个完整片段的第一次出现

    # 移除省略号，获取实际的内容部分
    cleaned_hit = hit_value.replace('...', '')

    # 首先尝试直接搜索最后一个完整的句子（包含句号）
    # 寻找以句号结尾的部分
    sentences = re.split(r'([。])', cleaned_hit)
    # 重新组合句子，保留句号
    complete_sentences = []
    for i in range(0, len(sentences) - 1, 2):
        if i + 1 < len(sentences) and sentences[i].strip():
            complete_sentence = sentences[i].strip() + (sentences[i + 1] if sentences[i + 1] == '。' else '')
            if len(complete_sentence) > 5:
                complete_sentences.append(complete_sentence)

    # 如果有完整的句子，尝试匹配最后一个
    if complete_sentences:
        last_sentence = complete_sentences[-1]
        pos = d_txt.find(last_sentence)
        if pos != -1:
            return pos + len(last_sentence)

    # 如果没有完整句子，分割为短语（但保留标点符号前的内容）
    parts = re.split(r'[。，；;]', cleaned_hit)
    meaningful_parts = [part.strip() for part in parts if part.strip() and len(part.strip()) > 5]

    if meaningful_parts:
        # 取最后一个有意义的部分
        last_meaningful = meaningful_parts[-1]

        # 在原文中搜索第一次出现的位置，并尝试包含后续的标点符号
        pos = d_txt.find(last_meaningful)
        if pos != -1:
            end_pos = pos + len(last_meaningful)
            # 检查后面是否有句号，如果有则包含
            if end_pos < len(d_txt) and d_txt[end_pos] == '。':
                end_pos += 1
            return end_pos

    # 如果找不到，尝试搜索包含"pT2N1bM1"的完整句子（确保包含句号）
    pT2N1bM1_pattern = r'pT2N1bM1[^。]*。'
    match = re.search(pT2N1bM1_pattern, d_txt)
    if match:
        return match.end()

    return None


def get_modify_evidence(hit_value, d_txt):
    """
    补齐evidence中间缺失/省略的部分
    """
    # 分割为子句
    hit_clauses = split_text_into_clauses(hit_value)
    original_clauses = split_text_into_clauses(d_txt)
    clause_positions = find_clause_positions_in_original(d_txt)

    # 找到最佳匹配序列
    match_indices = find_best_clause_sequence_match(hit_clauses, original_clauses, hit_value)

    if match_indices and has_missing_content_between_clauses(match_indices):
        # 获取完整的子句范围（包括缺失的中间子句）
        start_idx = match_indices[0]

        # 获取原文位置
        if start_idx < len(clause_positions):
            # 获取第一个匹配子句的起始位置
            first_clause_start = clause_positions[start_idx][0]

            # 针对省略号开头的情况，需要包含完整的字段名（如"术后病理示："）
            # 向前查找是否有字段名的完整形式
            if '...' in hit_value and start_idx > 0:
                # 检查前一个子句是否是同一个字段的开头部分
                prev_clause = original_clauses[start_idx]  # 当前匹配的子句

                # 在原文中向前查找，寻找可能的字段名开头
                search_start = max(0, first_clause_start - 50)  # 向前搜索50个字符
                search_text = d_txt[search_start:first_clause_start + len(prev_clause)]

                # 查找字段名模式（例如"术后病理示："）
                field_pattern = prev_clause + r'[：:]'
                field_match = re.search(field_pattern, search_text)
                if field_match:
                    # 找到完整字段名，从字段名开始
                    field_start_in_search = field_match.start()
                    original_start = search_start + field_start_in_search
                else:
                    original_start = first_clause_start
            else:
                original_start = first_clause_start

            # 使用智能结束位置查找
            smart_end = find_smart_end_position(hit_value, d_txt)
            if smart_end:
                original_end = smart_end
            else:
                # fallback: 使用最后一个匹配子句的结束位置，但排除明显不相关的内容
                end_idx = match_indices[-1]
                if end_idx < len(clause_positions):
                    candidate_end = clause_positions[end_idx][1]
                    # 检查这个位置是否合理（不包含基因结果等无关内容）
                    candidate_content = d_txt[original_start:candidate_end]
                    if not any(unwanted in candidate_content for unwanted in ['基因结果', 'BRAF', 'CT提示']):
                        original_end = candidate_end
                    else:
                        # 尝试使用前一个匹配位置
                        if len(match_indices) > 1:
                            prev_end_idx = match_indices[-2]
                            original_end = clause_positions[prev_end_idx][1]
                        else:
                            original_end = candidate_end
                else:
                    return None, None, None

            # 提取完整的原文内容（包括缺失的部分）
            complete_content = d_txt[original_start:original_end]

            return complete_content, original_start, original_end
        else:
            return None, None, None
    else:
        return None, None, None


def post_process_content_completion(evidence: json, d_txt: str) -> tuple[list[str], list[str], list[list[int]]]:
    """
    统一的内容补齐处理函数
    处理逻辑：
    1. 首先检查hit_value是否直接在d_txt中存在
    2. 如果不存在，则按子句进行匹配和补齐
    """
    original_evidences = []
    modified_evidences = []
    modified_evidence_indices = []

    for key, hit_value in evidence.items():
        if "hit_value" in key and hit_value:
            # 步骤1：直接检查hit_value是否在d_txt中存在
            if hit_value in d_txt:
                # logger.debug(f"hit_value直接存在于d_txt中，无需处理: {hit_value[:50]}...")
                continue

            # 步骤2：hit_value不在d_txt中，进行子句级别的匹配和补齐

            modified_evidence, original_start, original_end = get_modify_evidence(hit_value, d_txt)
            if hit_value and modified_evidence:
                original_evidences.append(hit_value)
                modified_evidences.append(modified_evidence)
                modified_evidence_indice = [original_start, original_end]
                modified_evidence_indices.append(modified_evidence_indice)

    return original_evidences, modified_evidences, modified_evidence_indices


def run():
    # 原有的Excel处理逻辑
    try:
        df = pd.read_excel("trace_bad_case_mock_data.xlsx")
        for i in range(len(df)):
            logger.info(f"========== 处理第 {i + 1} 行数据 ==========")
            d_txt = df.iloc[i]["D字段内容"]
            evidence = df.iloc[i]["API响应-答案（generation）"]

            # 处理不同的数据格式
            if isinstance(evidence, str):
                evidence = evidence.strip("[]").strip(" ")
                try:
                    # 首先尝试JSON解析
                    evidence = json.loads(evidence)
                except json.JSONDecodeError:
                    try:
                        # 如果JSON解析失败，尝试ast.literal_eval（处理Python字典格式）
                        evidence = ast.literal_eval(evidence)
                    except (ValueError, SyntaxError) as e:
                        logger.info(f"无法解析evidence: {e}")
                        logger.info(f"evidence内容: {evidence[:200]}...")
                        continue

            # 使用统一的内容补齐函数
            original_evidences, modified_evidences, modified_evidence_indices = post_process_content_completion(
                evidence,
                d_txt
            )

            if len(original_evidences) > 0:
                logger.info("========== 发现需要补齐的内容 ==========")
                for j in range(len(modified_evidences)):
                    logger.info(f"original evidence: {original_evidences[j]}")
                    logger.info(f"modified evidence: {modified_evidences[j]}")
                    logger.info(f"evidence_index: {modified_evidence_indices[j]}")
                    assert modified_evidences[j] == d_txt[modified_evidence_indices[j][0]:modified_evidence_indices[j][1]]
                    logger.info("=" * 50)
            else:
                logger.info("没有找到需要处理的内容")
    except FileNotFoundError:
        logger.info("Excel文件未找到，跳过Excel处理部分")
    except Exception as e:
        logger.info(f"处理Excel文件时出错: {e}")


if __name__ == "__main__":
    time1 = time.time()

    run()

    time2 = time.time()
    logger.info(f"运行时间: {time2 - time1}s")
