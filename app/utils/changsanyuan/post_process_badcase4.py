# -*- coding: utf-8 -*-
import re
import sys
import time
import json

import pandas as pd
from loguru import logger

# 独立管理日志级别
logger.remove()
log_level = 'INFO'
logger.add(sys.stderr, level=log_level)


def map_no_format_to_original(no_format_pos, original_d_txt):
    """将去除格式后的位置映射回原文位置"""
    current_no_format_pos = 0
    for i, char in enumerate(original_d_txt):
        # 计算在clean_text中保留的字符：中文、英文、数字和成对符号
        if (char.isalnum() or '\u4e00' <= char <= '\u9fff' or
                char in '<>[]"\'（）()【】{}《》'):
            if current_no_format_pos == no_format_pos:
                return i  # 返回当前有效字符的位置
            current_no_format_pos += 1
    return len(original_d_txt)


def clean_text(text):
    """清理文本，保留中文、英文、数字和成对符号"""
    # 保留中文字符、英文字母、数字和常见成对符号
    # 成对符号: <> [] "" '' （） () 【】 {} 《》
    return re.sub(r'[^\u4e00-\u9fff\w<>\[\]"\'（）()\【\】{}\《\》]', '', text)


def is_incomplete_paired_symbols(hit_value_str, original_text_str):
    """检查是否是成对符号不完整的情况"""
    # 定义成对符号
    paired_symbols = {
        '<': '>',
        '[': ']',
        '"': '"',
        "'": "'",
        '（': '）',
        '(': ')',
        '【': '】',
        '{': '}',
        '《': '》'
    }

    # 获取去除成对符号后的核心内容
    def get_core_content(text):
        # 移除所有成对符号
        for open_char, close_char in paired_symbols.items():
            text = text.replace(open_char, '').replace(close_char, '')
        return text.strip()

    hit_core = get_core_content(hit_value_str)
    original_core = get_core_content(original_text_str)

    # 如果核心内容相同，但原始文本不同，可能是成对符号问题
    if hit_core == original_core and hit_value_str != original_text_str:
        # 检查是否是成对符号的问题
        for open_char, close_char in paired_symbols.items():
            # 情况1：hit_value有完整成对符号，original缺失开头符号
            if (hit_value_str.startswith(open_char) and hit_value_str.endswith(close_char) and
                    not original_text_str.startswith(open_char) and original_text_str.endswith(close_char)):
                return True

            # 情况2：hit_value有完整成对符号，original缺失结尾符号
            if (hit_value_str.startswith(open_char) and hit_value_str.endswith(close_char) and
                    original_text_str.startswith(open_char) and not original_text_str.endswith(close_char)):
                return True

            # 情况3：hit_value有完整成对符号，original完全没有
            if (hit_value_str.startswith(open_char) and hit_value_str.endswith(close_char) and
                    open_char not in original_text_str and close_char not in original_text_str):
                return True

    return False


def has_format_difference(hit_value, original_text):
    """判断hit_value和原文是否存在格式差异"""
    hit_value_str = str(hit_value).strip()
    original_text_str = original_text.strip()

    # 如果内容完全相同，没有格式差异
    if hit_value_str == original_text_str:
        return False

    # 检查去除格式后的内容是否相同
    hit_value_clean = clean_text(hit_value_str)
    original_clean = clean_text(original_text_str)

    # 首先检查是否是成对符号不完整的情况
    if is_incomplete_paired_symbols(hit_value_str, original_text_str):
        return True

    # 如果清理后的内容不同，说明不是格式差异问题
    if hit_value_clean != original_clean:
        return False

    # 排除只有末尾标点符号差异的情况
    # 常见的末尾标点符号
    end_punctuation = '。，！？.,!?；;'

    # 去除末尾标点后比较
    hit_value_no_end_punct = hit_value_str.rstrip(end_punctuation)
    original_no_end_punct = original_text_str.rstrip(end_punctuation)

    # 如果去除末尾标点后内容相同，说明只是末尾标点差异，不需要处理
    if hit_value_no_end_punct == original_no_end_punct:
        return False

    # 如果清理后内容相同，但原始内容不同，说明存在需要处理的格式差异
    # logger.info(f"检测到格式差异:")
    # logger.info(f"  hit_value: '{hit_value_str}'")
    # logger.info(f"  original_text: '{original_text_str}'")
    # logger.info(f"  hit_value_clean: '{hit_value_clean}'")
    # logger.info(f"  original_clean: '{original_clean}'")
    return True


def get_modify_evidence(hit_value, d_txt):
    """
    处理evidence中格式差异的问题，使用更鲁棒的文本匹配
    """
    original_d_txt = d_txt
    d_txt_clean = clean_text(d_txt)
    # logger.info(f"d_txt_clean length: {len(d_txt_clean)}")

    hit_value_clean = clean_text(str(hit_value))
    # logger.info(f"hit_value: {hit_value}")
    # logger.info(f"hit_value_clean: {hit_value_clean}")
    # logger.info(f"hit_value_clean length: {len(hit_value_clean)}")

    if hit_value_clean in d_txt_clean:
        hit_start_clean = d_txt_clean.find(hit_value_clean)
        hit_end_clean = hit_start_clean + len(hit_value_clean)

        # logger.info(f"清除标点空格换行符后找到匹配，位置: {hit_start_clean} - {hit_end_clean}")

        original_start = map_no_format_to_original(hit_start_clean, original_d_txt)
        original_end = map_no_format_to_original(hit_end_clean, original_d_txt)

        original_text = original_d_txt[original_start:original_end]

        # logger.info(f"映射后原文位置: [{original_start}, {original_end}]")
        # logger.info(f"原文内容: '{original_text}'")

        # 只有当确实存在格式差异时才处理
        if has_format_difference(hit_value, original_text):
            # logger.info(f"发现格式差异:")
            # logger.info(f"  hit_value: '{hit_value}'")
            # logger.info(f"  原文内容: '{original_text}'")
            # logger.info(f"  位置: [{original_start}, {original_end}]")
            # logger.info("-" * 30)

            return original_text, original_start, original_end

    return None, None, None


def post_process_missing_linebreak(evidence: json, d_txt: str):
    original_evidences = []
    modified_evidences = []
    modified_evidence_indices = []

    for key, hit_value in evidence.items():
        if "hit_value" in key and hit_value:
            original_text, original_start, original_end = get_modify_evidence(hit_value, d_txt)
            if original_text is not None:
                original_evidences.append(hit_value)
                modified_evidences.append(original_text)
                modified_evidence_indices.append([original_start, original_end])

        # break

    return original_evidences, modified_evidences, modified_evidence_indices


def run():
    df = pd.read_excel("trace_bad_case_mock_data.xlsx")
    for i in range(len(df)):
        logger.info(f"=== 处理第 {i + 1} 行数据 ===")
        d_txt = df.iloc[i]["D字段内容"]
        evidence = df.iloc[i]["API响应-答案（generation）"]
        evidence = json.loads(evidence)

        # 调用处理函数
        original_evidences, modified_evidences, modified_evidence_indices = post_process_missing_linebreak(evidence,
                                                                                                           d_txt)
        if len(modified_evidences) > 0:
            for i in range(len(modified_evidences)):
                logger.info(f"original evidence: {original_evidences[i]}")
                logger.info(f"modified evidence: {modified_evidences[i]}")
                logger.info(f"evidence_index: {modified_evidence_indices[i]}")
                assert modified_evidences[i] == d_txt[modified_evidence_indices[i][0]:modified_evidence_indices[i][1]]
                logger.info("=" * 50)
                # break
        else:
            logger.info("没有找到缺失内容")

        logger.info('')

        # break


if __name__ == "__main__":
    time1 = time.time()

    run()

    time2 = time.time()
    logger.info(f"运行时间: {time2 - time1}s")
