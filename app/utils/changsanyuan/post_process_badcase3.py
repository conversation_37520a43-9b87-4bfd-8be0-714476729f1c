# -*- coding: utf-8 -*-
import sys
import json
import time

import pandas as pd
from loguru import logger

# 独立管理日志级别
logger.remove()
log_level = 'INFO'
logger.add(sys.stderr, level=log_level)


def has_wrong_punctuation(hit_value, original_text):
    """判断hit_value是否有错误的标点符号"""
    hit_value_str = str(hit_value).strip()
    original_text_str = original_text.strip()

    # 检查末尾标点符号是否不同
    hit_value_last_char = hit_value_str[-1] if hit_value_str else ""
    original_last_char = original_text_str[-1] if original_text_str else ""

    # 如果末尾字符不同，且其中一个是标点符号，则认为有错误
    punctuation_chars = ["。", "，", "！", "？", ".", ",", "!", "?", "；", ";"]

    # hit_value有错误的标点符号
    if (hit_value_last_char in punctuation_chars and
            original_last_char in punctuation_chars and
            hit_value_last_char != original_last_char):
        return True

    return False


def get_modify_evidence(hit_value, d_txt):
    """
    处理错误标点符号
    """
    hit_value_str = str(hit_value)

    # 去掉末尾标点符号来查找主要内容
    hit_value_no_punctuation = hit_value_str.rstrip('。，！？.,!?；;')

    # 在原文中查找对应内容
    original_start = d_txt.find(hit_value_no_punctuation)
    if original_start != -1:
        # 计算可能的结束位置
        content_end = original_start + len(hit_value_no_punctuation)

        # 检查原文中紧跟的字符是否是标点符号
        original_end = content_end
        if content_end < len(d_txt) and d_txt[content_end] in ["。", "，", "！", "？", ".", "!", "?", "；", ";"]:
            original_end = content_end + 1

        original_text = d_txt[original_start:original_end]

        # 只有当确实存在错误标点符号时才处理
        if has_wrong_punctuation(hit_value_str, original_text):
            logger.debug(f"发现错误标点符号:")
            logger.debug(f"  hit_value: '{hit_value_str}'")
            logger.debug(f"  原文内容: '{original_text}'")
            logger.debug(f"  hit_value末尾: '{hit_value_str[-1] if hit_value_str else ''}'")
            logger.debug(f"  原文末尾: '{original_text[-1] if original_text else ''}'")
            logger.debug(f"  位置: [{original_start}, {original_end}]")
            logger.debug("-" * 30)

            return original_text, original_start, original_end

    return None, None, None


def post_process_wrong_dots(evidence: json, d_txt: str):
    original_evidences = []
    modified_evidences = []
    modified_evidence_indices = []

    for key, hit_value in evidence.items():
        if "hit_value" in key and hit_value:
            original_text, original_start, original_end = get_modify_evidence(hit_value, d_txt)
            if original_text is not None:
                original_evidences.append(hit_value)
                modified_evidences.append(original_text)
                modified_evidence_indices.append([original_start, original_end])

        # break

    return original_evidences, modified_evidences, modified_evidence_indices


def run():
    df = pd.read_excel("trace_bad_case_mock_data.xlsx")

    for row_idx in range(len(df)):
        logger.info(f"=== 处理第 {row_idx + 1} 行数据 ===")
        d_txt = df.iloc[row_idx]["D字段内容"]
        evidence = df.iloc[row_idx]["API响应-答案（generation）"]
        evidence = json.loads(evidence)

        # 调用处理函数
        original_evidences, modified_evidences, modified_evidence_indices = post_process_wrong_dots(evidence, d_txt)

        if len(modified_evidences) > 0:
            for i in range(len(modified_evidences)):
                logger.info(f"original evidence: '{original_evidences[i]}'")
                logger.info(f"modified evidence: '{modified_evidences[i]}'")
                logger.info(f"evidence_index: {modified_evidence_indices[i]}")
                assert modified_evidences[i] == d_txt[modified_evidence_indices[i][0]:modified_evidence_indices[i][1]]
                logger.info("=" * 50)
        else:
            logger.info("没有找到错误标点符号")

        logger.info('')

        # break


if __name__ == "__main__":
    time1 = time.time()

    run()

    time2 = time.time()
    logger.info(f"运行时间: {time2 - time1}s")
