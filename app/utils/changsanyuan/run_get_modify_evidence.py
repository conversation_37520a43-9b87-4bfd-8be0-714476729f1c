# -*- coding: utf-8 -*-
import os
import sys
import time

from loguru import logger

# 处理evidence结尾错误标点符号
from app.utils.changsanyuan.post_process_badcase3 import get_modify_evidence as get_modify_evidence_type_3
# 处理evidence中格式差异问题
from app.utils.changsanyuan.post_process_badcase4 import get_modify_evidence as get_modify_evidence_type_1
# 补齐evidence中间缺失/省略的部分
from app.utils.changsanyuan.post_process_badcase6 import get_modify_evidence as get_modify_evidence_type_2

logger.remove()
log_level = 'INFO'
filename = os.path.basename(__file__)
log_dir = os.path.join('logs', filename.split('.')[0])
log_file = os.path.join(log_dir, '{time:YYYY-MM-DD}.log')
logger.add(sys.stderr, level=log_level)
logger.add(log_file, level=log_level, rotation="00:00", enqueue=True, serialize=False, encoding="utf-8")


def read_txt(file):
    if not os.path.exists(file):
        return Exception(f'{file} does not exist')
    with open(file, 'r', encoding='utf-8') as f:
        text = f.read()
    return text


def get_evidence(hit_value, d_text):
    """
    在源文本中查找目标文本
    """
    idx = d_text.find(hit_value)
    if idx != -1:
        return d_text[idx: idx + len(hit_value)], idx, idx + len(hit_value)
    return None, None, None


def get_modify_evidence(hit_value, d_text):
    """
    evidence(hit_value)获取/更新主函数
    """
    original_text, original_start, original_end = get_evidence(hit_value, d_text)
    if original_text is None:
        original_text, original_start, original_end = get_modify_evidence_type_3(hit_value, d_text)
        if original_text is None:
            original_text, original_start, original_end = get_modify_evidence_type_1(hit_value, d_text)
            if original_text is None:
                original_text, original_start, original_end = get_modify_evidence_type_2(hit_value, d_text)
                if original_text is None:
                    return None, None, None
                else:
                    return original_text, original_start, original_end
            else:
                return original_text, original_start, original_end
        else:
            return original_text, original_start, original_end
    else:
        return original_text, original_start, original_end


def run():
    d_text = read_txt('demo_d_text.txt')
    hit_value = read_txt('demo_hit_value.txt')

    d_text ='''
    就诊时间：2025-01-20 17:30
主诉：J项目计划外访视
现病史：左乳浸润性癌肝转移（ER+PR-Her-2（2+，FISH阴性），IV期）
既往史：未诉特殊
过敏史：造影剂
体格检查：无特殊
辅助检查：-
诊断：左乳浸润性癌肝转移（ER+PR-Her-2（2+，FISH阴性），IV期）
处理：受试者今日监测指尖血糖结果示：15.2mmol/L，嘱受试者立即外院购买盐酸二甲双胍片降糖处理并嘱受害者今日第二次服药暂停，直至高血糖转归至G1（7.0mmol/L-11.1mmol/L）后恢复用药。
病史：
1、小儿麻痹症：1级，1973年开始，未治疗，随访观察；
2、慢性乙型病毒性肝炎：2级，2023/09/14开始，对症治疗，随访观察；
3、双手麻木：2级，2024/12开始，对症治疗，随访观察；
4、淋巴细胞计数降低：1级，2025/01/10开始，未治疗，随访观察；
5、贫血：1级，2025/01/10开始，未治疗，随访观察；
6、低白蛋白血症：1级，2025/01/10开始，未治疗，随访观察；
7、ALT计数升高：2级，2025/01/10开始，对症治疗，随访观察；
8、AST计数升高：2级，2025/01/10开始，对症治疗，随访观察；
9、GGT计数升高：2级，2025/01/10开始，2025/01/14升为3级，对症治疗，随访观察；
10、高尿酸血症：1级，2025/01/10开始，未治疗，随访观察；
11、高脂血症：1级，2025/01/10开始，未治疗，随访观察；
AE:
1、GGT计数升高：cs 3级，2025/01/14开始，与试验药物关系：肯定无关，对试验药物采取措施：剂量不变，不为SAE，对症治疗，随访观察；
2、高糖血症：cs 1级，2025/01/14开始，与试验药物关系：可能有关，对试验药物采取措施：剂量不变，不为SAE，未治疗，随访观察（嘱受试者每日监测血糖）；
合并用药：
1、恩替卡韦分散片：2024/10开始，0.5mg，po，qd，用于治疗慢性乙型病毒性肝炎；
2、甲钴胺片：2025/01/07开始，0.5mg，po，tid，用于治疗双手麻木；
3、多烯磷脂酰胆碱胶囊：2024/12/06开始，456mg，po，tid，用于保肝及治疗ALT计数升高、AST计数升高、GGT计数升高；
4、多烯磷脂酰胆碱注射液：2025/01/07-2025/01/15，456mg，ivgtt，qd，保肝；
5、异甘草酸镁注射液：2025/01/07-2025/01/15，100mg，ivgtt，qd，保肝；
复诊意见：如患者有病情变化或不适请及时到医院就诊
病人途径：无
医生签名：
    '''
    hit_value = '主诉：J项目计划外访视'

    logger.info(f'd_text: \n{d_text}')
    logger.debug(f'd_text_length: {len(d_text)}')
    logger.info(f'hit_value(original_evidence): {hit_value}')
    logger.info(f'hit_value_length: {len(hit_value)}')
    logger.info('')

    original_text, original_start, original_end = get_modify_evidence(hit_value, d_text)

    logger.info(f'modified_evidence: {original_text}')
    logger.info(f'modified_evidence_length: {len(original_text)}')
    logger.info(f'modified_evidence_index: [{original_start}, {original_end}]')

    assert original_text == d_text[original_start:original_end]

    logger.info('')
    logger.info(f'modified_evidence == d_text[{original_start}:{original_end}] assert ok')
    logger.info('')


if __name__ == "__main__":
    time1 = time.time()

    run()

    time2 = time.time()
    logger.info(f"运行时间: {time2 - time1}s")
