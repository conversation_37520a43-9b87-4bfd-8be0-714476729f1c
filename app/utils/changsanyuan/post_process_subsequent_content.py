import pandas as pd
import re
import json 

def PostProcess_RedundantSpaces(evidence: json, D_txt: str) -> tuple[list[str], list[int]]:
    """
    Post-process the evidence and D_txt to get the final result.
    去掉D_txt和hit_value中的空格，找到匹配的字段，返回字段后续内容和索引
    难点在于找到字段名结束位置
    """
    # 创建原始D_txt的副本
    original_D_txt = D_txt
    
    # 去掉D_txt中的所有空格用于搜索
    D_txt_no_spaces = D_txt.replace(" ", "")
    
    found_texts = []
    found_indices = []
    
    for key, hit_value in evidence.items():
        if "hit_value" in key:
            hit_key = re.split("[,，？?_]", key)[0]
            # print(f"Processing: {hit_key}, {hit_value}")
            
            # 去掉hit_value中的所有空格
            hit_value_no_spaces = str(hit_value).replace(" ", "")#.replace("\t", "").replace("\n", "").replace("\r", "")
            
            # 在无空格的D_txt中查找hit_value
            if hit_value_no_spaces in D_txt_no_spaces:
                # 找到hit_value的结束位置（在无空格文本中）
                hit_start_pos_no_spaces = D_txt_no_spaces.find(hit_value_no_spaces)
                hit_end_pos_no_spaces = hit_start_pos_no_spaces + len(hit_value_no_spaces)
                
                # 在D_txt_no_spaces中查找内容的结束位置
                content_end_no_spaces = len(D_txt_no_spaces)  # 默认到文本末尾
                
                # 从hit_value结束位置开始，在无空格文本中查找下一个字段
                for i in range(hit_end_pos_no_spaces, len(D_txt_no_spaces)):
                    # 检查当前位置是否可能是字段名的开始
                    remaining_no_spaces = D_txt_no_spaces[i:i+20]
                    
                    # 生成一个去除空格的字段名列表用于匹配
                    medical_fields_no_spaces = []
                    medical_fields = [
                        "辅助检查", "辅 助 检 查", "体格检查", "体 格 检 查", 
                        "主诉", "主 诉", "现病史", "现 病 史", "既往史", "既 往 史",
                        "个人史", "个 人 史", "家族史", "家 族 史", "查体", "查 体",
                        "专科检查", "专 科 检 查", "实验室检查", "实 验 室 检 查",
                        "影像学检查", "影 像 学 检 查", "诊断", "诊 断", "治疗", "治 疗"
                    ]
                    
                    for field in medical_fields:
                        field_no_spaces = field.replace(" ", "")
                        medical_fields_no_spaces.append(field_no_spaces)
                    
                    # 检查是否匹配任何医疗字段名（无空格版本）
                    for field_no_spaces in medical_fields_no_spaces:
                        if remaining_no_spaces.startswith(field_no_spaces):
                            content_end_no_spaces = i
                            # print(f"在无空格文本中找到字段名: {field_no_spaces}, 位置{i}")
                            break
                    
                
                # 现在将无空格文本中的位置映射回原始文本
                def map_no_space_pos_to_original(no_space_pos):
                    """将无空格文本中的位置映射到原始文本位置"""
                    current_no_space_pos = 0
                    for i, char in enumerate(original_D_txt):
                        if current_no_space_pos == no_space_pos:
                            return i
                        if char != " ":
                            current_no_space_pos += 1
                    return len(original_D_txt)
                
                # 映射hit_value结束位置和内容结束位置
                original_hit_end_pos = map_no_space_pos_to_original(hit_end_pos_no_spaces)
                original_content_end_pos = map_no_space_pos_to_original(content_end_no_spaces)
                
                # print(f"hit_value结束位置: {original_hit_end_pos}")
                # print(f"内容结束位置: {original_content_end_pos}")
                
                # 从hit_value结束位置开始，跳过分隔符
                content_start = original_hit_end_pos
                while content_start < len(original_D_txt) and original_D_txt[content_start] in [" ", "\t", "：", ":", "=", "；", ";", "，", ","]:
                    content_start += 1
                
                # 提取后续内容
                if content_start < original_content_end_pos:
                    subsequent_text = original_D_txt[content_start:original_content_end_pos].strip()
                    if subsequent_text:  # 只有当有实际内容时才添加
                        found_texts.append(subsequent_text)
                        found_indices.append([content_start, original_content_end_pos])
                        # print(f"Found field '{hit_value}' with subsequent content: '{subsequent_text}' at position [{content_start}, {original_content_end_pos}]")
        if "体格检查" in hit_value:
            break
    # 返回所有找到的后续文本和索引
    if found_texts:
        return found_texts, found_indices
    else:
        return "", []

if __name__ == "__main__":
    df = pd.read_excel("溯源badcase_分析_20250616.xlsx")
    
    
    for i in range(len(df)):
        D_txt = df.iloc[i]["D字段内容"]
        evidence = df.iloc[i]["API响应-答案（generation）"]
        evidence = json.loads(evidence)
        
        # 调用处理函数
        result_text, result_indices = PostProcess_RedundantSpaces(evidence, D_txt)
        print(f"modified evidence: {result_text}")
        print(f"evidence_index: {result_indices}")
        print("="*50)
        
        # 可以选择只处理第一条记录进行测试
        break

    # evidence,evidence_index = PostProcess_RedundantSpaces(evidence, D_txt)
    # print(evidence,evidence_index)