# text_locator.py
import re
import logging
from typing import Optional
logger = logging.getLogger(__name__)

class Position:
    """封装起始和结束位置"""
    def __init__(self, start: int, end: int):
        self.start = start
        self.end = end

    def __eq__(self, other):
        if not isinstance(other, Position):
            return NotImplemented
        return self.start == other.start and self.end == other.end

    def __repr__(self): # 添加 repr 以便测试输出更清晰
        return f"Position(start={self.start}, end={self.end})"

def find_start_position(original_text: str, context: str, value: str) -> Optional[Position]:
    """
    在原始文本段落中根据上下文查找目标值的起始位置和结束位置。

    Args:
        original_text: 原始文本的独立段落。
        context: 目标值及其周围的上下文文本。
        value: 需要定位的目标值。

    Returns:
        包含 start 和 end 的 Position 对象。如果找不到唯一匹配，则返回 None。
    """
    logger.debug(f"开始查找文本位置 - 原文长度: {len(original_text) if original_text else 0}, "
                f"上下文长度: {len(context) if context else 0}, "
                f"目标值: '{value}'")
    
    if not original_text or not context or not value:
        logger.warning(f"输入参数无效 - 原文为空: {not original_text}, 上下文为空: {not context}, 目标值为空: {not value}")
        return None # 基本的无效输入检查

    if value not in context:
        # 如果目标值本身就不在上下文中，无法定位
        logger.warning(f"目标值 '{value}' 不在上下文中，无法定位")
        return None

    # 1. 尝试使用完整上下文进行精确匹配
    try:
        # 使用正则表达式查找所有可能的 context 匹配项
        # re.escape 确保 context 中的特殊字符被视为字面量
        context_matches = list(re.finditer(re.escape(context), original_text))
        logger.debug(f"完整上下文匹配数量: {len(context_matches)}")

        possible_positions = [] # 重命名以反映包含 Position 对象
        for i, match in enumerate(context_matches):
            context_start = match.start()
            # 在找到的 context 内部查找 value
            try:
                # value_start_in_context 是 value 相对于 context 开头的偏移量
                value_start_in_context = context.index(value)
                # 计算 value 在 original_text 中的绝对起始位置
                absolute_value_start = context_start + value_start_in_context
                # 验证 original_text 中对应位置的内容确实是 value
                if original_text[absolute_value_start : absolute_value_start + len(value)] == value:
                    end_position = absolute_value_start + len(value)
                    possible_positions.append(Position(start=absolute_value_start, end=end_position))
                    logger.debug(f"完整上下文匹配 #{i+1} - 找到有效位置: {absolute_value_start}-{end_position}")
                else:
                    logger.debug(f"完整上下文匹配 #{i+1} - 位置验证失败: 预期值 '{value}', "
                                f"实际值 '{original_text[absolute_value_start : absolute_value_start + len(value)]}'")
            except ValueError:
                # value 不在 context 中（理论上前面检查过了，但以防万一）
                logger.warning(f"完整上下文匹配 #{i+1} - 目标值在上下文中定位失败，这不应该发生")
                continue

        # 如果找到一个或多个精确匹配，返回第一个 (新逻辑)
        if len(possible_positions) > 0:
            logger.info(f"使用完整上下文找到 {len(possible_positions)} 个匹配，返回第一个: {possible_positions[0]}")
            return possible_positions[0]
        # 如果 possible_positions 为空，则完整上下文没有找到有效匹配，继续尝试缩小
        logger.debug("完整上下文未找到有效匹配，尝试缩小上下文范围")

    except re.error as e:
        # 正则表达式错误处理
        logger.error(f"完整上下文正则表达式错误: {str(e)}")
        return None
    # 注意：如果上面返回了，这里的代码不会执行

    # 2. 如果完整上下文未找到匹配，尝试缩小上下文范围
    # 策略：优先尝试包含 value 的、最长的子上下文，然后逐渐变短
    try:
        # 确保 value 在 context 中 (虽然前面检查过，但这里是缩小的起点)
        value_start_in_context = context.index(value)
        value_end_in_context = value_start_in_context + len(value)
        logger.debug(f"目标值在上下文中的位置: {value_start_in_context}-{value_end_in_context}")

        # 从最长可能的子串（长度为 len(context)-1）开始，到最短包含 value 的子串
        for length in range(len(context) - 1, len(value) - 1, -1):
            logger.debug(f"尝试长度为 {length} 的子上下文")
            # 检查所有该长度的子串
            for i in range(len(context) - length + 1):
                sub_context = context[i : i + length]

                # 优化：仅当 value 在当前 sub_context 中时才进行搜索
                if value not in sub_context:
                    continue

                # 使用缩小的上下文查找
                try:
                    sub_context_matches = list(re.finditer(re.escape(sub_context), original_text))
                    logger.debug(f"子上下文 '{sub_context[:20]}...' 匹配数量: {len(sub_context_matches)}")
                    
                    possible_positions_sub = []

                    for j, match in enumerate(sub_context_matches):
                        sub_context_start = match.start()
                        try:
                            # value_start_in_sub_context 是 value 相对于 sub_context 开头的偏移量
                            value_start_in_sub_context = sub_context.index(value)
                            # 计算 value 在 original_text 中的绝对起始位置
                            absolute_value_start = sub_context_start + value_start_in_sub_context
                            # 验证
                            if original_text[absolute_value_start : absolute_value_start + len(value)] == value:
                                end_position = absolute_value_start + len(value)
                                possible_positions_sub.append(Position(start=absolute_value_start, end=end_position))
                                logger.debug(f"子上下文匹配 #{j+1} - 找到有效位置: {absolute_value_start}-{end_position}")
                            else:
                                logger.debug(f"子上下文匹配 #{j+1} - 位置验证失败: 预期值 '{value}', "
                                            f"实际值 '{original_text[absolute_value_start : absolute_value_start + len(value)]}'")
                        except ValueError:
                            # value 不在 sub_context 中（理论上被外层 if value not in sub_context 挡住）
                            logger.warning(f"子上下文匹配 #{j+1} - 目标值在子上下文中定位失败，这不应该发生")
                            continue

                    # 如果使用当前缩小的上下文找到了一个或多个匹配，立即返回第一个 (新逻辑)
                    if len(possible_positions_sub) > 0:
                        logger.info(f"使用子上下文 '{sub_context[:20]}...' 找到 {len(possible_positions_sub)} 个匹配，返回第一个: {possible_positions_sub[0]}")
                        return possible_positions_sub[0]
                    # 如果当前 sub_context 没有找到匹配，继续尝试同长度的其他 sub_context

                except re.error as e:
                    # 如果当前 sub_context 正则有问题，跳过它继续尝试其他 sub_context
                    logger.warning(f"子上下文 '{sub_context[:20]}...' 正则表达式错误: {str(e)}")
                    continue
            # 如果当前 length 的所有 sub_context 都没有找到匹配，继续尝试更短的 length
            logger.debug(f"长度为 {length} 的所有子上下文都未找到匹配")

    except ValueError:
         # value 不在 context 中（理论上开头检查过了）
         logger.warning("目标值在上下文中定位失败，这不应该发生")
         return None
    # re.error 在内层 try-except 中处理了

    # 3. 如果所有尝试（完整上下文和所有缩小上下文）都失败
    logger.warning(f"所有尝试都失败，无法定位目标值 '{value}'")
    logger.warning(f"原文片段: '{original_text[:100]}...'")
    logger.warning(f"上下文: '{context}'")
    return None
