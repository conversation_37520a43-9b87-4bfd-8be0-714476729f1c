import json
import logging
from typing import Dict, Any, List, Union, Optional
import re
from langchain_core.output_parsers import BaseOutputParser
from pydantic import Field

logger = logging.getLogger(__name__)


class DuplicateKeyJsonParser(BaseOutputParser):
    """
    自定义JSON解析器，能够处理重复键名问题
    
    当JSON中存在重复键名时，自动为重复的键添加序号后缀
    例如：重复的"如异常，请说明"会被重命名为"如异常，请说明_2", "如异常，请说明_3"等
    """
    
    # Pydantic字段声明
    pydantic_object: Optional[Any] = Field(default=None, description="兼容LangChain JsonOutputParser的参数")
    key_counters: Dict[str, int] = Field(default_factory=dict, description="用于跟踪键名出现次数")
    
    def __init__(self, pydantic_object=None, **kwargs):
        """
        初始化解析器
        
        Args:
            pydantic_object: 兼容Lang<PERSON><PERSON><PERSON> JsonOutputParser的参数，暂时保留用于接口兼容
        """
        super().__init__(pydantic_object=pydantic_object, key_counters={}, **kwargs)
        
    def parse(self, text: str) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        解析包含重复键的JSON文本
        
        Args:
            text: 要解析的JSON文本
            
        Returns:
            解析后的字典或列表，重复键已被重命名
        """
        try:
            # 清理输入文本，移除 markdown 代码块标记等格式
            text = self._clean_input_text(text)
            
            # 先尝试标准JSON解析，如果成功且没有重复键，直接返回
            try:
                result = json.loads(text)
                if not self._has_duplicate_keys(text):
                    return result
            except json.JSONDecodeError:
                pass  # 如果标准解析失败，继续使用自定义解析
            
            # 使用自定义解析处理重复键
            logger.info("检测到重复键，使用自定义解析器处理")
            parsed_result = self._parse_with_duplicate_key_handling(text)
            
            logger.info(f"成功解析包含重复键的JSON，结果类型: {type(parsed_result)}")
            return parsed_result
            
        except Exception as e:
            logger.error(f"DuplicateKeyJsonParser解析失败: {e}")
            logger.debug(f"解析失败的文本: {text}")
            raise
    
    def get_format_instructions(self) -> str:
        """
        返回格式化指令，供LangChain使用
        
        Returns:
            格式化指令字符串
        """
        return "输出应为有效的JSON格式。如果包含重复键名，解析器将自动为重复键添加序号后缀。"
    
    def _clean_input_text(self, text: str) -> str:
        """
        清理输入文本，移除 markdown 代码块标记等格式
        
        Args:
            text: 原始输入文本
            
        Returns:
            清理后的纯 JSON 文本
        """
        if not text or not text.strip():
            return text
        
        # 移除前后空白
        cleaned_text = text.strip()
        
        # 检查是否包含 markdown 代码块格式
        markdown_patterns = [
            r'^```json\s*\n(.*?)\n```$',  # ```json ... ```
            r'^```\s*\n(.*?)\n```$',     # ``` ... ```
            r'^```json\s*(.*?)```$',     # ```json...``` (单行)
            r'^```\s*(.*?)```$'          # ```...``` (单行)
        ]
        
        for pattern in markdown_patterns:
            match = re.search(pattern, cleaned_text, re.DOTALL | re.MULTILINE)
            if match:
                extracted_content = match.group(1).strip()
                logger.info(f"检测到 markdown 代码块格式，提取纯 JSON 内容")
                logger.debug(f"原始文本长度: {len(text)}, 清理后长度: {len(extracted_content)}")
                return extracted_content
        
        # 如果没有找到 markdown 格式，返回原始文本
        logger.debug("未检测到 markdown 代码块格式，返回原始文本")
        return cleaned_text
    
    def _has_duplicate_keys(self, text: str) -> bool:
        """
        检查JSON文本是否包含重复键
        
        Args:
            text: JSON文本
            
        Returns:
            是否包含重复键
        """
        try:
            # 使用正则表达式提取所有键名
            key_pattern = r'"([^"]+)"\s*:'
            keys = re.findall(key_pattern, text)
            
            # 检查是否有重复
            return len(keys) != len(set(keys))
        except Exception:
            return False
    
    def _parse_with_duplicate_key_handling(self, text: str) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        处理包含重复键的JSON解析
        
        Args:
            text: JSON文本
            
        Returns:
            解析后的结果
        """
        # 重置键计数器
        self.key_counters = {}
        
        # 检查是否是数组格式
        stripped_text = text.strip()
        if stripped_text.startswith('[') and stripped_text.endswith(']'):
            # 处理JSON数组
            return self._parse_json_array(stripped_text)
        else:
            # 处理JSON对象
            return self._parse_json_object(stripped_text)
    
    def _parse_json_array(self, text: str) -> List[Dict[str, Any]]:
        """
        解析JSON数组，处理每个对象中的重复键
        
        Args:
            text: JSON数组文本
            
        Returns:
            解析后的数组
        """
        # 移除外层的方括号
        inner_text = text.strip()[1:-1].strip()
        
        # 分割数组元素（简单实现，假设对象内部不包含逗号分隔的数组）
        objects = self._split_array_objects(inner_text)
        
        result = []
        for obj_text in objects:
            if obj_text.strip():
                parsed_obj = self._parse_json_object(obj_text.strip())
                if parsed_obj:
                    result.append(parsed_obj)
        
        return result
    
    def _split_array_objects(self, text: str) -> List[str]:
        """
        分割JSON数组中的对象
        
        Args:
            text: 数组内部文本
            
        Returns:
            对象文本列表
        """
        objects = []
        brace_count = 0
        current_obj = ""
        
        i = 0
        while i < len(text):
            char = text[i]
            current_obj += char
            
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    # 完整对象结束
                    objects.append(current_obj)
                    current_obj = ""
                    # 跳过后面的逗号和空白
                    i += 1
                    while i < len(text) and text[i] in ', \n\t\r':
                        i += 1
                    i -= 1  # 因为循环会自动+1
            
            i += 1
        
        # 处理最后一个对象（如果没有结束大括号）
        if current_obj.strip():
            objects.append(current_obj)
        
        return objects
    
    def _parse_json_object(self, text: str) -> Dict[str, Any]:
        """
        解析单个JSON对象，处理重复键
        
        Args:
            text: JSON对象文本
            
        Returns:
            解析后的字典
        """
        # 移除外层的大括号
        if text.strip().startswith('{') and text.strip().endswith('}'):
            inner_text = text.strip()[1:-1].strip()
        else:
            inner_text = text.strip()
        
        # 解析键值对
        result = {}
        key_value_pairs = self._extract_key_value_pairs(inner_text)
        
        for key, value in key_value_pairs:
            # 处理重复键
            unique_key = self._get_unique_key(key)
            result[unique_key] = value
        
        return result
    
    def _extract_key_value_pairs(self, text: str) -> List[tuple]:
        """
        从JSON对象内部文本中提取键值对
        
        Args:
            text: 对象内部文本
            
        Returns:
            键值对列表
        """
        pairs = []
        i = 0
        
        while i < len(text):
            # 跳过空白字符
            while i < len(text) and text[i] in ' \n\t\r':
                i += 1
            
            if i >= len(text):
                break
            
            # 提取键
            if text[i] == '"':
                key, i = self._extract_quoted_string(text, i)
            else:
                # 处理没有引号的键（虽然不标准，但有时会出现）
                key, i = self._extract_unquoted_key(text, i)
            
            # 跳过空白和冒号
            while i < len(text) and text[i] in ' \n\t\r:':
                i += 1
            
            # 提取值
            value, i = self._extract_value(text, i)
            
            pairs.append((key, value))
            
            # 跳过逗号
            while i < len(text) and text[i] in ' \n\t\r,':
                i += 1
        
        return pairs
    
    def _extract_quoted_string(self, text: str, start: int) -> tuple:
        """
        提取引号包围的字符串
        
        Args:
            text: 文本
            start: 起始位置（引号位置）
            
        Returns:
            (字符串内容, 结束位置)
        """
        i = start + 1  # 跳过开始的引号
        result = ""
        
        while i < len(text):
            if text[i] == '\\':
                # 处理转义字符
                if i + 1 < len(text):
                    result += text[i:i+2]
                    i += 2
                else:
                    result += text[i]
                    i += 1
            elif text[i] == '"':
                # 结束引号
                return result, i + 1
            else:
                result += text[i]
                i += 1
        
        return result, i
    
    def _extract_unquoted_key(self, text: str, start: int) -> tuple:
        """
        提取没有引号的键名
        
        Args:
            text: 文本
            start: 起始位置
            
        Returns:
            (键名, 结束位置)
        """
        i = start
        result = ""
        
        while i < len(text) and text[i] not in ' \n\t\r:':
            result += text[i]
            i += 1
        
        return result.strip(), i
    
    def _extract_value(self, text: str, start: int) -> tuple:
        """
        提取JSON值
        
        Args:
            text: 文本
            start: 起始位置
            
        Returns:
            (值, 结束位置)
        """
        i = start
        
        # 跳过空白
        while i < len(text) and text[i] in ' \n\t\r':
            i += 1
        
        if i >= len(text):
            return None, i
        
        # 判断值的类型
        if text[i] == '"':
            # 字符串值
            value, end_pos = self._extract_quoted_string(text, i)
            return value, end_pos
        elif text[i] == '{':
            # 对象值（递归处理）
            return self._extract_object_value(text, i)
        elif text[i] == '[':
            # 数组值
            return self._extract_array_value(text, i)
        elif text[i:i+4].lower() == 'null':
            # null值
            return None, i + 4
        elif text[i:i+4].lower() == 'true':
            # true值
            return True, i + 4
        elif text[i:i+5].lower() == 'false':
            # false值
            return False, i + 5
        else:
            # 数字值
            return self._extract_number_value(text, i)
    
    def _extract_object_value(self, text: str, start: int) -> tuple:
        """
        提取对象值
        
        Args:
            text: 文本
            start: 起始位置（左大括号位置）
            
        Returns:
            (对象值, 结束位置)
        """
        brace_count = 0
        i = start
        
        while i < len(text):
            if text[i] == '{':
                brace_count += 1
            elif text[i] == '}':
                brace_count -= 1
                if brace_count == 0:
                    # 找到完整对象
                    obj_text = text[start:i+1]
                    try:
                        obj_value = self._parse_json_object(obj_text)
                        return obj_value, i + 1
                    except:
                        # 如果解析失败，返回原始文本
                        return obj_text, i + 1
            i += 1
        
        # 如果没有找到结束大括号，返回剩余文本
        return text[start:], len(text)
    
    def _extract_array_value(self, text: str, start: int) -> tuple:
        """
        提取数组值
        
        Args:
            text: 文本
            start: 起始位置（左方括号位置）
            
        Returns:
            (数组值, 结束位置)
        """
        bracket_count = 0
        i = start
        
        while i < len(text):
            if text[i] == '[':
                bracket_count += 1
            elif text[i] == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    # 找到完整数组
                    array_text = text[start:i+1]
                    try:
                        array_value = json.loads(array_text)
                        return array_value, i + 1
                    except:
                        # 如果解析失败，返回原始文本
                        return array_text, i + 1
            i += 1
        
        # 如果没有找到结束方括号，返回剩余文本
        return text[start:], len(text)
    
    def _extract_number_value(self, text: str, start: int) -> tuple:
        """
        提取数字值
        
        Args:
            text: 文本
            start: 起始位置
            
        Returns:
            (数字值, 结束位置)
        """
        i = start
        number_str = ""
        
        # 处理负号
        if i < len(text) and text[i] == '-':
            number_str += text[i]
            i += 1
        
        # 处理数字
        while i < len(text) and (text[i].isdigit() or text[i] in '.eE+-'):
            number_str += text[i]
            i += 1
        
        # 尝试转换为数字
        try:
            if '.' in number_str or 'e' in number_str.lower():
                return float(number_str), i
            else:
                return int(number_str), i
        except ValueError:
            # 如果转换失败，返回字符串
            return number_str, i
    
    def _get_unique_key(self, key: str) -> str:
        """
        获取唯一的键名，如果键已存在则添加序号后缀
        
        Args:
            key: 原始键名
            
        Returns:
            唯一的键名
        """
        if key not in self.key_counters:
            # 第一次出现，直接使用原键名
            self.key_counters[key] = 1
            return key
        else:
            # 重复出现，添加序号后缀
            self.key_counters[key] += 1
            unique_key = f"{key}_{self.key_counters[key]}"
            logger.info(f"检测到重复键 '{key}'，重命名为 '{unique_key}'")
            return unique_key
    
    @property
    def _type(self) -> str:
        """返回解析器类型"""
        return "duplicate_key_json"


def create_duplicate_key_parser(pydantic_object=None) -> DuplicateKeyJsonParser:
    """
    创建DuplicateKeyJsonParser实例的工厂函数
    
    Args:
        pydantic_object: 兼容参数
        
    Returns:
        DuplicateKeyJsonParser实例
    """
    return DuplicateKeyJsonParser(pydantic_object=pydantic_object) 