import os
from typing import List, Dict, Any
import numpy as np
from pymilvus import MilvusClient, FieldSchema, DataType, CollectionSchema, Collection
from langchain_core.documents import Document
from app.utils.embedding import get_embedding_model, get_embedding_dimension

# 初始化 MilvusClient
client = MilvusClient(
    uri=os.getenv("MILVUS_URI", "http://localhost:19530")
)


def create_collection_if_not_exists(collection_name: str):
    """创建集合（如果不存在）"""
    # 检查集合是否存在
    collections = client.list_collections()
    if collection_name in collections:
        return

    # 这段代码已不再使用，但保留注释以便理解历史实现
    # 定义集合字段 - 注意：现在使用MilvusClient.create_schema方法创建模式
    # vector_dim = get_embedding_dimension()
    # fields = [
    #     FieldSchema(name='id', dtype=DataType.VARCHAR, description='primary key', max_length=100, is_primary=True,
    #                 auto_id=True),
    #     FieldSchema(name='doc_id', dtype=DataType.VARCHAR, description='document id', max_length=500),
    #     FieldSchema(name='chunk_id', dtype=DataType.VARCHAR, description='chunk id', max_length=500),
    #     FieldSchema(name='content', dtype=DataType.VARCHAR, description='document content', max_length=65535),
    #     FieldSchema(name='source', dtype=DataType.VARCHAR, description='document source', max_length=500),
    #     FieldSchema(name='metadata', dtype=DataType.JSON, description='document metadata'),
    #     FieldSchema(name='vector', dtype=DataType.FLOAT_VECTOR, description='document embedding vector', dim=vector_dim)
    # ]

    # 获取嵌入模型的向量维度
    vector_dim = get_embedding_dimension()

    # 创建集合模式
    schema = MilvusClient.create_schema(
        auto_id=True,
        enable_dynamic_field=True,
    )
    schema.add_field(field_name="id",datatype=DataType.INT64,is_primary=True)
    schema.add_field(field_name="doc_id",datatype=DataType.VARCHAR,max_length=500)
    schema.add_field(field_name="chunk_id",datatype=DataType.VARCHAR,max_length=500)
    schema.add_field(field_name="content",datatype=DataType.VARCHAR,max_length=65535)
    schema.add_field(field_name="source",datatype=DataType.VARCHAR,max_length=500)
    schema.add_field(field_name="metadata",datatype=DataType.JSON,max_length=500)
    schema.add_field(field_name="vector",datatype=DataType.FLOAT_VECTOR,dim=vector_dim)

    index_params = client.prepare_index_params()

    index_params.add_index(
        field_name="doc_id",
        index_type="AUTOINDEX"
    )

    index_params.add_index(
        field_name="vector",
        index_type="AUTOINDEX",
        metric_type="COSINE"
    )
    # 创建集合
    client.create_collection(
        collection_name=collection_name,
        schema=schema,
        index_params=index_params
    )



    # 加载集合到内存
    res = client.get_load_state(
        collection_name=collection_name
    )
    print(res)


def store_documents(documents: List[Document], collection_name: str):
    """将文档存储到Milvus，如果doc_id已存在则先删除再插入"""
    create_collection_if_not_exists(collection_name)

    # 收集所有文档的doc_id
    doc_ids = [doc.metadata.get("doc_id", "") for doc in documents if doc.metadata.get("doc_id", "")]

    # 检查哪些doc_id已经存在
    if doc_ids:
        # 构建查询表达式，检查哪些doc_id已存在
        doc_ids_str = ', '.join([f'"{doc_id}"' for doc_id in doc_ids])
        expr = f'doc_id in [{doc_ids_str}]'

        # 删除已存在的记录
        try:
            delete_result = client.delete(
                collection_name=collection_name,
                filter=expr
            )
            print(f"已删除 {delete_result.get('delete_count')} 条重复记录")
        except Exception as e:
            print(f"删除重复记录时出错: {e}")

    # 准备插入的数据
    entities = []
    for doc in documents:
        # 获取嵌入向量
        vector = get_embedding_model().embed_query(doc.page_content)

        # 为每个文档创建一个实体
        entity = {
            "doc_id": doc.metadata.get("doc_id", ""),
            "chunk_id": doc.metadata.get("chunk_id", ""),
            "content": doc.page_content,
            "source": doc.metadata.get("source", ""),
            "metadata": doc.metadata,
            "vector": vector
        }
        entities.append(entity)

    # 插入数据
    insert_result = client.insert(collection_name=collection_name, data=entities)

    return len(documents)


def search_documents(collection_name: str, query: str, filter_dict: Dict[str, Any] = None, top_k: int = 5):
    """搜索文档"""
    # 检查集合是否存在
    collections = client.list_collections()
    if collection_name not in collections:
        raise ValueError(f"Collection {collection_name} does not exist")

    # 获取查询的嵌入向量
    query_vector = get_embedding_model().embed_query(query)

    # 构建过滤表达式
    expr = None
    if filter_dict:
        if "doc_id" in filter_dict:
            # 处理单个doc_id的情况（向后兼容）
            doc_id_value = filter_dict["doc_id"]
            expr = f'doc_id == "{doc_id_value}"'
            print(f"DEBUG - Filter expression: {expr}")
        elif "doc_ids" in filter_dict and filter_dict["doc_ids"]:
            # 处理doc_ids数组的情况
            doc_ids = filter_dict["doc_ids"]
            if len(doc_ids) == 1:
                expr = f'doc_id == "{doc_ids[0]}"'
            else:
                # 构建IN表达式：doc_id in ["id1", "id2", ...]
                doc_ids_str = ', '.join([f'"{doc_id}"' for doc_id in doc_ids])
                expr = f'doc_id in [{doc_ids_str}]'
            print(f"DEBUG - Filter expression: {expr}")

    # 执行搜索
    search_params = {
        "metric_type": "COSINE",
        "params": {"ef": 32}
    }

    results = client.search(
        collection_name=collection_name,
        data=[query_vector],
        filter=expr,
        limit=top_k,
        output_fields=["doc_id", "chunk_id", "content", "source", "metadata"],
        search_params=search_params
    )


    return results



def check_collection_schema(client: MilvusClient, collection_name: str):
    """检查集合的模式"""
    # 检查集合是否存在
    collections = client.list_collections()
    if collection_name not in collections:
        return f"Collection {collection_name} does not exist"

    # 使用 MilvusClient 的 describe_collection 方法获取集合信息
    collection_info = client.describe_collection(collection_name=collection_name)

    # 提取模式信息
    schema_info = {
        "name": collection_name,
        "schema": collection_info,
        "description": collection_info.get("description", ""),
        "fields": collection_info.get("schema", {}).get("fields", [])
    }

    return schema_info
