"""
访问数据格式化工具

提供将各种格式的visits数据转换为符合app/domain/schemas.py中定义的Visit模型格式的功能
"""
import logging
from typing import List, Dict, Any, Optional, Union
from app.domain.schemas import Visit, VisitData, Record

logger = logging.getLogger(__name__)

def format_visits_data(visits_data: List[Any]) -> List[Visit]:
    """
    将各种格式的visits数据转换为符合Visit模型格式的对象列表
    
    Args:
        visits_data: 原始visits数据列表，可能是Visit对象或字典
        
    Returns:
        List[Visit]: 格式化后的Visit对象列表
    """
    formatted_visits = []
    
    try:
        # 遍历visits数据，确保每个visit都符合Visit模型的格式
        for visit_data in visits_data:
            # 检查是否已经是Visit对象
            if hasattr(visit_data, 'visitId') and hasattr(visit_data, 'visitType') and hasattr(visit_data, 'data'):
                # 已经是Visit对象，直接添加
                formatted_visits.append(visit_data)
            else:
                # 不是Visit对象，需要转换
                # 检查是否是字典
                if isinstance(visit_data, dict):
                    visit_dict = format_visit_dict(visit_data)
                    if visit_dict:
                        try:
                            visit_obj = Visit(**visit_dict)
                            formatted_visits.append(visit_obj)
                        except Exception as e:
                            logger.error(f"转换Visit对象时出错: {str(e)}")
                            # 记录详细的错误信息，便于调试
                            logger.error(f"错误的visit_dict: {visit_dict}")
        
        return formatted_visits
    except Exception as e:
        logger.error(f"格式化visits数据时出错: {str(e)}")
        return []

def format_visit_dict(visit_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    将visit字典格式化为符合Visit模型格式的字典
    
    Args:
        visit_data: 原始visit字典
        
    Returns:
        Optional[Dict[str, Any]]: 格式化后的visit字典，如果格式化失败则返回None
    """
    try:
        # 确保必要的字段存在
        visit_id = visit_data.get('visitId')
        visit_type = visit_data.get('visitType', '未知类型')
        visit_data_list = visit_data.get('data', [])
        
        # 确保visitId是整数
        if isinstance(visit_id, str) and visit_id.isdigit():
            visit_id = int(visit_id)
        elif not isinstance(visit_id, int):
            visit_id = 0  # 默认值
        
        # 处理data字段，确保符合VisitData模型的格式
        formatted_data = []
        for data_item in visit_data_list:
            formatted_data_item = format_visit_data_dict(data_item)
            if formatted_data_item:
                formatted_data.append(formatted_data_item)
        
        # 创建Visit字典
        return {
            'visitId': visit_id,
            'visitType': visit_type,
            'data': formatted_data
        }
    except Exception as e:
        logger.error(f"格式化visit字典时出错: {str(e)}")
        return None

def format_visit_data_dict(data_item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    将visitData字典格式化为符合VisitData模型格式的字典
    
    Args:
        data_item: 原始visitData字典
        
    Returns:
        Optional[Dict[str, Any]]: 格式化后的visitData字典，如果格式化失败则返回None
    """
    try:
        if isinstance(data_item, dict):
            # 确保必要的字段存在
            doc_id = data_item.get('docId', '')
            is_struct = data_item.get('isStruct', False)
            table_name_desc = data_item.get('tableNameDesc', '')
            table_name = data_item.get('tableName', '')
            records = data_item.get('records', [])
            
            # 处理records字段，确保符合Record模型的格式
            formatted_records = []
            for record in records:
                formatted_record = format_record_dict(record)
                if formatted_record:
                    formatted_records.append(formatted_record)
            
            # 创建VisitData字典
            return {
                'docId': doc_id,
                'isStruct': is_struct,
                'tableNameDesc': table_name_desc,
                'tableName': table_name,
                'records': formatted_records
            }
        return None
    except Exception as e:
        logger.error(f"格式化visitData字典时出错: {str(e)}")
        return None

def format_record_dict(record: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    将record字典格式化为符合Record模型格式的字典
    
    Args:
        record: 原始record字典
        
    Returns:
        Optional[Dict[str, Any]]: 格式化后的record字典，如果格式化失败则返回None
    """
    try:
        if isinstance(record, dict):
            content = record.get('content', '')
            record_id = record.get('recordId', '')
            return {
                'content': content,
                'recordId': record_id
            }
        return None
    except Exception as e:
        logger.error(f"格式化record字典时出错: {str(e)}")
        return None
