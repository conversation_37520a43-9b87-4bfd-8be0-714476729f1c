"""
OpenRouter 配置工具函数
用于解析和构建 OpenRouter API 的 provider 配置
"""
import os
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

def parse_provider_list(provider_str: str) -> List[str]:
    """
    解析逗号分隔的供应商字符串为列表
    
    Args:
        provider_str: 逗号分隔的供应商字符串
        
    Returns:
        List[str]: 供应商名称列表
    """
    if not provider_str or not provider_str.strip():
        return []
    
    # 分割字符串并去除空白
    providers = [p.strip() for p in provider_str.split(",") if p.strip()]
    return providers

def build_openrouter_provider_config() -> Dict[str, Any]:
    """
    根据环境变量构建 OpenRouter 的 provider 配置
    
    Returns:
        Dict[str, Any]: provider 配置字典，用于 extra_body
    """
    # 读取环境变量
    provider_order_str = os.getenv("OPENROUTER_PROVIDER_ORDER", "Anthropic,Together")
    allow_fallbacks_str = os.getenv("OPENROUTER_ALLOW_FALLBACKS", "true").lower()
    provider_ignore_str = os.getenv("OPENROUTER_PROVIDER_IGNORE", "")
    
    # 解析配置
    provider_order = parse_provider_list(provider_order_str)
    allow_fallbacks = allow_fallbacks_str == "true"
    provider_ignore = parse_provider_list(provider_ignore_str)
    
    # 构建配置字典
    provider_config = {}
    
    if provider_order:
        provider_config["order"] = provider_order
        logger.debug(f"OpenRouter provider order: {provider_order}")
    
    if provider_ignore:
        provider_config["ignore"] = provider_ignore
        logger.debug(f"OpenRouter provider ignore: {provider_ignore}")
    
    # 设置 allow_fallbacks
    provider_config["allow_fallbacks"] = allow_fallbacks
    logger.debug(f"OpenRouter allow_fallbacks: {allow_fallbacks}")
    
    return provider_config

def get_openrouter_extra_args() -> Dict[str, Any]:
    """
    获取 OpenRouter API 调用的额外参数
    
    Returns:
        Dict[str, Any]: 包含 extra_body 的参数字典
    """
    provider_config = build_openrouter_provider_config()
    
    if not provider_config:
        logger.debug("No OpenRouter provider configuration found, using empty extra_args")
        return {}
    
    extra_args = {
        "extra_body": {
            "provider": provider_config
        }
    }
    
    logger.info(f"Built OpenRouter extra_args: {extra_args}")
    return extra_args

def get_openrouter_max_tokens() -> int:
    """
    获取 OpenRouter 的最大 token 配置
    
    Returns:
        int: 最大 token 数量
    """
    try:
        max_tokens = int(os.getenv("OPENROUTER_MAX_TOKENS", "32000"))
        logger.debug(f"OpenRouter max_tokens: {max_tokens}")
        return max_tokens
    except ValueError as e:
        logger.warning(f"Invalid OPENROUTER_MAX_TOKENS value, using default 32000: {e}")
        return 32000

def log_openrouter_config():
    """
    记录当前的 OpenRouter 配置信息，用于调试
    """
    logger.info("=== OpenRouter Configuration ===")
    logger.info(f"Provider Order: {os.getenv('OPENROUTER_PROVIDER_ORDER', 'Anthropic,Together')}")
    logger.info(f"Allow Fallbacks: {os.getenv('OPENROUTER_ALLOW_FALLBACKS', 'true')}")
    logger.info(f"Provider Ignore: {os.getenv('OPENROUTER_PROVIDER_IGNORE', '(none)')}")
    logger.info(f"Max Tokens: {os.getenv('OPENROUTER_MAX_TOKENS', '32000')}")
    
    # 构建并显示最终配置
    provider_config = build_openrouter_provider_config()
    logger.info(f"Final Provider Config: {provider_config}")
    logger.info("=== End OpenRouter Configuration ===") 