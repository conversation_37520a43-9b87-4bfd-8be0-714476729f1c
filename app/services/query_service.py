from app.models.schemas import QueryRequest, QueryResponse, DocumentResult, QueryRAGResponse
from app.utils.vector_store import search_documents


class QueryService:
    def query(self, request: QueryRequest):
        """处理知识库查询，返回标准化的检索结果"""
        # 构建过滤条件
        filter_dict = {}
        if request.doc_ids:
            # 使用doc_ids数组
            filter_dict["doc_ids"] = request.doc_ids
        elif request.doc_id:
            # 向后兼容，支持单个doc_id
            filter_dict["doc_id"] = request.doc_id

        # 搜索相关文档
        search_results = search_documents(
            collection_name=request.collection_name,
            query=request.question,
            filter_dict=filter_dict,
            top_k=5
        )

        # 格式化返回结果
        results = []
        sources = []

        # 处理搜索结果
        if search_results and len(search_results) > 0:
            for hit in search_results[0]:
                # 创建DocumentResult对象
                doc_result = DocumentResult(
                    content=hit.get("entity").get("content"),
                    metadata=hit.get("entity").get("metadata")
                )
                results.append(doc_result)

                # 添加来源信息
                source = hit.get("entity").get("source")
                if source and source not in sources:
                    sources.append(source)
        resp = QueryRAGResponse(
            question=request.question,
            results=results,
            sources=sources)
        # 返回标准化的响应
        print(resp)
        return resp
