import uuid
from datetime import datetime
from typing import Dict, List

from langchain_core.documents import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter

from app.models.schemas import KnowledgeUploadRequest
from app.utils.vector_store import store_documents

class KnowledgeService:
    def process_and_store(self, request: KnowledgeUploadRequest) -> Dict:
        """处理文档并存储到向量数据库"""
        # 参数验证
        if not request.documents:
            raise ValueError("Empty documents")

        if request.chunk_overlap >= request.chunk_size:
            raise ValueError("chunk_overlap must be smaller than chunk_size")

        # 初始化文本分割器（针对中文优化）
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=request.chunk_size,
            chunk_overlap=request.chunk_overlap,
            separators=["\n\n", "。", "！", "？", "\n", "，", " ", ""]
        )

        # 处理所有文档
        processed_docs = []
        for doc in request.documents:
            content = doc.content.strip()
            if not content:
                continue

            # 分割文本
            chunks = text_splitter.split_text(content)

            # 构建带元数据的文档块
            for idx, chunk in enumerate(chunks):
                metadata = doc.metadata.copy()
                metadata.update({
                    "chunk_id": str(uuid.uuid4()),
                    "upload_time": datetime.now().isoformat(),
                    "chunk_index": idx + 1,
                    "total_chunks": len(chunks),
                    "source": metadata.get("source", "unknown"),
                    "doc_id": doc.doc_id  # 添加文档ID到元数据
                })
                processed_docs.append(Document(
                    page_content=chunk,
                    metadata=metadata
                ))

        if not processed_docs:
            raise ValueError("No valid content to process")

        # 存储文档到向量数据库
        processed_count = store_documents(processed_docs, request.collection_name)

        return {
            "status": "success",
            "collection": request.collection_name,
            "processed_chunks": processed_count
        }
