import logging
from typing import Dict, Any, Optional, List, Literal, Union
from pydantic import BaseModel, Field, ValidationError, model_validator

logger = logging.getLogger(__name__)

# --- 数据模型定义 (Pydantic Schemas) ---

class TimeRange(BaseModel):
    """时间范围模型"""
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")

class TimeEntity(BaseModel):
    """时间实体模型"""
    type: Literal[
        "date_point",       # 例如 "2024-07-15", "今天下午3点"
        "date_range",       # 例如 "昨天到今天", "2024-07-15 08:00 到 12:00"
        "relative_time",    # 例如 "最近一次", "昨天"
        "frequency",        # 例如 "每小时", "每天两次"
        "key_points"        # 例如 ["08:00", "12:00", "16:00"]
    ] = Field(..., description="时间实体的类型")
    value: Union[str, TimeRange, List[str], Dict] = Field(..., description="时间实体的值")
    details: Optional[Dict[str, str]] = Field(None, description="其他时间相关的细节")
    biz_time_type: Optional[str] = Field(None, description="业务时间类型，如'就诊时间'、'业务时间'、'首次访视时间'")
    time_type: Optional[int] = Field(None, description="时间类型，1:最近一次，2:时间范围，3:大于等于，4:小于等于，5:时间点")

class FillFormDetails(BaseModel):
    """表单填写意图的详细信息"""
    form_name: Optional[str] = Field(None, description="要处理的表单名称，例如 '结构化', '纸质', '病历'" )
    table_code: Optional[str] = Field(None, description="table_code" )
    subject_id: Optional[str] = Field(None, description="涉及的受试者ID")
    time_entities: Optional[List[TimeEntity]] = Field(default_factory=list, description="从用户请求中提取的时间相关实体")
    orderBy: Optional[Literal['latest']] = Field(None, description="排序要求，目前仅支持 'latest'")

    # 确保 time_entities 字段在为 None 时被转换为空列表
    @model_validator(mode='after')
    def ensure_time_entities_is_list(self):
        if self.time_entities is None:
            self.time_entities = []
            logger.warning("time_entities was None, converted to empty list")
        return self

class IntentOutput(BaseModel):
    """意图识别节点的输出模型"""
    intent: Literal["Fill_Form", "Refill_Form", "other"] = Field(..., description="识别出的用户主要意图")
    source: Optional[Literal["Structure", "Paper", "Medical_Record"]] = Field(None, description="仅当意图为 Refill_Form 时，表示期望的数据来源")
    details: Optional[FillFormDetails] = Field(None, description="意图为 Fill_Form 或 Refill_Form 时的详细信息")

    # Pydantic v2 使用 model_validator
    @model_validator(mode='after')
    def check_intent_details_consistency(self):
        if self.intent == "other" and self.details is not None:
            logger.warning("Details field should be null when intent is 'other', but found data. Clearing details.")
            self.details = None # 强制清空
            # raise ValueError("Details must be null when intent is 'other'") # 或者严格报错
        if self.intent != "other" and self.details is None:
            # 如果意图不是 'other'，details 理论上应该有，但 LLM 可能不返回
            logger.warning(f"Details field is null for intent '{self.intent}'. This might be expected or an LLM issue.")
        return self

    @model_validator(mode='after')
    def check_source_consistency(self):
        if self.intent != "Refill_Form" and self.source is not None:
            logger.warning("Source field should be null when intent is not 'Refill_Form', but found data. Clearing source.")
            self.source = None # 强制清空
            # raise ValueError("Source must be null when intent is not Refill_Form") # 或者严格报错
        if self.intent == "Refill_Form" and self.source is None:
             # Refill_Form 意图下，source 理论上应该由 LLM 判断给出，但允许为 None 以处理 LLM 可能的遗漏
             logger.warning("Source is null for Refill_Form intent. Defaulting may be needed downstream or LLM/prompt needs refinement.")
        return self

# --- End Placeholder Schemas ---