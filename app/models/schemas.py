from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict


class DocumentUpload(BaseModel):
    """单个文档上传结构"""
    doc_id: str  # 新增文档唯一标识
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class KnowledgeUploadRequest(BaseModel):
    """知识库上传请求结构"""
    collection_name: str
    documents: List[DocumentUpload]
    chunk_size: int = Field(default=512, gt=100, le=2048)
    chunk_overlap: int = Field(default=50, ge=0, le=200)


class QueryRequest(BaseModel):
    """查询请求结构"""
    question: str
    collection_name: str = "default"
    enable_web: bool = False
    doc_id: Optional[str] = None  # 新增可选的文档ID参数
    doc_ids: Optional[List[str]] = None  # 新增字段，支持多个doc_id


# Models based on KnowledgeSlice.java
class KnowledgeSlicePosition(BaseModel):
    context: Optional[str] = None
    pageNo: Optional[int] = None
    start: Optional[int] = None
    end: Optional[int] = None


class KnowledgeSlice(BaseModel):
    fileId: Optional[str] = None
    fileName: Optional[str] = None
    fileUrl: Optional[str] = None
    tag: Optional[int] = None
    start: Optional[int] = None
    end: Optional[int] = None
    context: Optional[str] = None
    position: Optional[KnowledgeSlicePosition] = None


# Models based on AbstractApiResponse.java
class DataSource(BaseModel):
    docId: Optional[str] = None
    recordId: Optional[str] = None
    hitValue: Optional[str] = None
    tableNameDesc: Optional[str] = None
    tableName: Optional[str] = None
    columnName: Optional[str] = None
    startPointIndex: Optional[int] = None
    endPointIndex: Optional[int] = None


class AbstractApiResponse(BaseModel):
    thinkLogic: Optional[str] = None
    thinkDataSources: Optional[List[DataSource]] = None
    knowledgeSlice: Optional[List[KnowledgeSlice]] = None
    answer: Optional[str] = None
    recommendedQuestion: Optional[List[str]] = None


# Models based on IJudgeCrfResult.java
class CrfTableValueItem(BaseModel):
    crfIndexName: Optional[str] = None
    crfIndexValue: Optional[str] = None
    crfItemId: Optional[int] = None  # Java Long maps to Python int
    crfIndexCode: Optional[str] = None


class IJudgeCrfResult(BaseModel):
    crfTableName: Optional[str] = None
    csJudgement: Optional[int] = None
    csJudgementDsc: Optional[str] = None
    crfTableValue: Optional[List[CrfTableValueItem]] = None


# Provided DocumentResult and QueryResponse for context (not derived from Java)
class DocumentResult(BaseModel):
    content: str
    metadata: Dict[str, Any]


class QueryResponse(BaseModel):
    crfResult: Optional[List[IJudgeCrfResult]] = None


class QueryRAGResponse(BaseModel):
    """查询响应结构"""
    question: str
    results: List[DocumentResult]
    sources: List[str]
