from fastapi import FastAPI
from dotenv import load_dotenv
import os

from app.controllers.knowledge_controller import router as knowledge_router
from app.controllers.pv_controller import router as pv_router
from app.controllers.stream_controller import router as stream_router


def create_app(lifespan=None) -> FastAPI:
    # 加载环境变量
    load_dotenv()

    app = FastAPI(
        title="Schema-Driven Tool Execution API",
        description="An API to execute predefined tools based on a configuration file.",
        version="0.2.0",
        lifespan=lifespan
    )

    # 注册路由
    app.include_router(knowledge_router, prefix="/api/v1")
    app.include_router(pv_router)
    app.include_router(stream_router)

    return app
