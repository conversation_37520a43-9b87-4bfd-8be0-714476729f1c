"""Web API 模块 - 提供 FastAPI 接口"""
import logging
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from app.utils.stream_llm import call_llm_api_stream
from typing import AsyncGenerator


# Configure logging with detailed timestamp
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

router = APIRouter()


class TextAnalysisRequest(BaseModel):
    input_text: str = Field(..., description="需要分析的输入文本")


@router.post("/api/analyze_text/stream")
async def pvExtractStream(request: TextAnalysisRequest) -> StreamingResponse:
    provider = "dashscope"
    model = "deepseek-r1"
    prompt_text = request.input_text

    async def stream_generator() -> AsyncGenerator[str, None]:
        think_logic = ""
        content = ""
        for chunk in call_llm_api_stream(
            prompt=prompt_text,
            provider=provider,
            model=model,
            temperature=0
        ):
            if chunk["thinkLogic"]:
                think_logic += chunk["thinkLogic"]
                result = {
                    "event": "thinking",
                    "content": chunk["thinkLogic"]
                }
                yield f"data: {result}\n\n"
            elif chunk["content"]:
                content += chunk["content"]
                result = {
                    "event": "answering",
                    "content": chunk["content"]
                }
                yield f"data: {result}\n\n"
        result = {
            "event": "complete",
            "data": {
                "thinkLogic": think_logic,
                "answer": content
            }
        }
        yield f"data: {result}\n\n"
        logger.info(f"thinkLogic: {think_logic} \ncontent: {content}")
    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
