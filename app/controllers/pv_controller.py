"""Web API 模块 - 提供 FastAPI 接口"""
import logging
from fastapi import HTTPException, APIRouter
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator
import time

from app.domain.schemas import PvAssistantAppParams
from app.application.nodes.pv_nodes import pv_extract_form_data_stream

# Configure logging with detailed timestamp
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/api/pv/extract/stream")
async def pvExtractStream(request: PvAssistantAppParams) -> StreamingResponse:
    """处理pv表单录入请求，流式返回"""
    try:
        app_params = request

        # 检查并设置默认值 (与原代码相同)
        app_params.prompt = app_params.prompt if app_params.prompt is not None else ""
        app_params.chatType = app_params.chatType if app_params.chatType is not None else "standard"
        app_params.questions = app_params.questions if app_params.questions is not None else []

        start_time = time.time()

        async def stream_generator() -> AsyncGenerator[str, None]:
            try:
                for chunk in pv_extract_form_data_stream(app_params, None):
                    # logger.info(f"chunk in main: {chunk}")
                    yield f"data: {chunk}\n\n"
                elapsed_time = time.time() - start_time
                # 记录结束trace和耗时
                logger.info(f"TRACE - 结束执行 - 耗时: {elapsed_time:.4f}秒")
            except Exception as invoke_exc:
                logger.error(f"Error in streaming pvExtractStream: {invoke_exc}", exc_info=True)
                err_msg = {
                    "event": "error",
                    "content": str(invoke_exc)
                }
                yield f"data: {err_msg}\n\n"

        return StreamingResponse(
            stream_generator(), 
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error in pvExtractStream endpoint: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="处理请求时发生意外错误")