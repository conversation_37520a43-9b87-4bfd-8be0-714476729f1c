from fastapi import APIRouter, HTTPException
from app.models.schemas import KnowledgeUploadRequest, QueryRequest, QueryResponse, QueryRAGResponse
from app.services.knowledge_service import KnowledgeService
from app.services.query_service import QueryService

router = APIRouter()
knowledge_service = KnowledgeService()
query_service = QueryService()


@router.post("/knowledge")
async def upload_knowledge(req: KnowledgeUploadRequest):
    """知识库上传接口 - 将文档添加到向量数据库"""
    try:
        result = knowledge_service.process_and_store(req)

        # 如果result是字典类型
        if isinstance(result, dict):
            # 直接返回处理结果
            return result
        else:
            # 如果是数字，按原计划处理
            return {
                "status": "success",
                "message": f"成功处理并存储了{result}个文档片段",
                "chunks_stored": result
            }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理知识库时发生错误: {str(e)}")


@router.post("/query", response_model=QueryRAGResponse)
async def query_knowledge(req: QueryRequest):
    """知识检索接口 - 从向量数据库中检索相关内容"""
    try:
        response = query_service.query(req)
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=f"检索失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检索过程中发生错误: {str(e)}")
