import psycopg2
from dotenv import load_dotenv
from psycopg2 import pool
import logging
import requests
import json
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import os

load_dotenv()
class DatabaseConnector:
    def __init__(self):
        # 创建数据库连接池
        self.db_prime_dr_pool = psycopg2.pool.SimpleConnectionPool(
            1, 10,
            host=os.getenv("DB_PRIME_DR_HOST"),
            database=os.getenv("DB_PRIME_DR_DATABASE"),
            user=os.getenv("DB_PRIME_DR_USER"),
            password=os.getenv("DB_PRIME_DR_PASSWORD"),
            port=os.getenv("DB_PRIME_DR_PORT"),
            options=f"-c search_path={os.getenv('DB_PRIME_DR_SCHEMA')}"
        )

        self.db_prime_direct_pool = psycopg2.pool.SimpleConnectionPool(
            1, 10,
            host=os.getenv("DB_PRIME_DIRECT_HOST"),
            database=os.getenv("DB_PRIME_DIRECT_DATABASE"),
            user=os.getenv("DB_PRIME_DIRECT_USER"),
            password=os.getenv("DB_PRIME_DIRECT_PASSWORD"),
            port=os.getenv("DB_PRIME_DIRECT_PORT"),
            options=f"-c search_path={os.getenv('DB_PRIME_DIRECT_SCHEMA')}"
        )

        self.logger = logging.getLogger(__name__)

    def get_db_prime_dr_connection(self):
        return self.db_prime_dr_pool.getconn()

    def get_db_prime_direct_connection(self):
        return self.db_prime_direct_pool.getconn()

    def release_connection(self, conn, is_prime_dr=True):
        if is_prime_dr:
            self.db_prime_dr_pool.putconn(conn)
        else:
            self.db_prime_direct_pool.putconn(conn)

    def close_all_connections(self):
        if self.db_prime_dr_pool:
            self.db_prime_dr_pool.closeall()
        if self.db_prime_direct_pool:
            self.db_prime_direct_pool.closeall()


def execute_workflow_query(
        db_connector: DatabaseConnector,
        table_code: str,
        hospital_no: str,
        pro_id: str,
        empi_id: str,
        pts_id: str,
        time_type: Optional[int] = None,  # 时间类型
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        biz_start_time: Optional[str] = None,
        biz_end_time: Optional[str] = None
) -> List[Dict]:
    """
    执行工作流查询，整合多个子查询结果

    Args:
        db_connector: 数据库连接器
        table_code: 表代码
        hospital_no: 医院编号 (必需)
        pro_id: 项目ID (必需)
        empi_id: 患者EMPI ID (必需)
        pts_id: PTS ID (必需)
        time_type: 时间类型 (可选, 1: 最近一次, 2: 时间范围, 3: 大于等于, 4: 小于等于, 5: 时间点)
        start_time: 开始时间 (可选)
        end_time: 结束时间 (可选)
        biz_start_time: 业务开始时间 (可选)
        biz_end_time: 业务结束时间 (可选)

    Returns:
        查询结果列表
    """
    try:
        # 1. 执行子查询1: 获取visit信息
        sub_node_visit = get_visit_info(
            db_connector,
            empi_id,
            hospital_no,
            start_time,
            end_time,
            pts_id
        )

        if not sub_node_visit:
            logging.info(f"未找到符合条件的就诊信息: empi_id={empi_id}, hospital_no={hospital_no}")
            return []

        # 2. 执行子查询2: 获取业务时间列
        biz_time_column = get_biz_time_column(db_connector, table_code)

        if not biz_time_column:
            logging.warning(f"未找到表 {table_code} 的业务时间列")
            return []

        # 3. 执行子查询3: 获取业务状态条件
        biz_status_condition = get_biz_status_condition(db_connector, table_code)

        if not biz_status_condition:
            logging.warning(f"未找到表 {table_code} 的业务状态条件，使用默认条件 1=1")
            biz_status_condition = "1=1"

        # 4. 构建并执行主查询
        result = execute_main_query(
            db_connector,
            table_code,
            sub_node_visit,
            biz_time_column,
            biz_status_condition,
            hospital_no,
            pro_id,
            time_type,  # 传递时间类型
            biz_start_time,
            biz_end_time
        )

        return result

    except Exception as e:
        logging.error(f"执行工作流查询出错: {str(e)}")
        raise e


def get_visit_info(
        db_connector: DatabaseConnector,
        empi_id: str,
        hospital_no: str,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        pts_id: str = None
) -> str:
    """获取就诊信息"""
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_dr_connection()
        cursor = conn.cursor()

        # 构建基础查询
        query_parts = [
            "SELECT concat(visit_id, visit_type)",
            "FROM empi_registrations",
            "WHERE empi_patient_id = %s",
            "AND hospital_no = %s"
        ]
        params = [empi_id, hospital_no]

        # 如果提供了时间范围，添加时间条件
        if start_time and end_time:
            # 检查开始和结束时间是否相同且只有日期部分（不包含时间）
            is_same_date_only = (start_time == end_time and
                                len(start_time) <= 10 and ":" not in start_time)

            if is_same_date_only:
                # 如果是同一天且只有日期，将查询范围设置为该天的开始到结束
                query_parts.append("AND visit_time BETWEEN %s AND %s")
                params.extend([f"{start_time} 00:00:00", f"{start_time} 23:59:59"])
                logging.info(f"查询就诊信息: {start_time}，范围从 00:00:00 到 23:59:59")
            else:
                query_parts.append("AND visit_time BETWEEN %s AND %s")
                params.extend([start_time, end_time])
                logging.info(f"查询就诊信息: {start_time} 至 {end_time}")
        elif start_time:
            # 检查是否只有日期部分（不包含时间）
            is_date_only = len(start_time) <= 10 and ":" not in start_time

            if is_date_only:
                # 如果只有日期，将查询范围设置为该天的开始到结束
                query_parts.append("AND visit_time BETWEEN %s AND %s")
                params.extend([f"{start_time} 00:00:00", f"{start_time} 23:59:59"])
                logging.info(f"查询就诊信息: {start_time}，范围从 00:00:00 到 23:59:59")
            else:
                query_parts.append("AND visit_time >= %s")
                params.append(start_time)
                logging.info(f"查询就诊信息: 大于等于 {start_time}")
        elif end_time:
            # 检查是否只有日期部分（不包含时间）
            is_date_only = len(end_time) <= 10 and ":" not in end_time

            if is_date_only:
                # 如果只有日期，将查询范围设置为该天的开始到结束
                query_parts.append("AND visit_time BETWEEN %s AND %s")
                params.extend([f"{end_time} 00:00:00", f"{end_time} 23:59:59"])
                logging.info(f"查询就诊信息: {end_time}，范围从 00:00:00 到 23:59:59")
            else:
                query_parts.append("AND visit_time <= %s")
                params.append(end_time)
                logging.info(f"查询就诊信息: 小于等于 {end_time}")

        # # 如果提供了pts_id，添加排除条件
        # if pts_id:
        #     query_parts.append("""
        #     AND concat(visit_id, visit_type) NOT IN (
        #         SELECT concat(visit_id, visit_type)
        #         FROM iqc_document_status_group
        #         WHERE pts_id = %s
        #         AND status = 0
        #     )
        #     """)
        #     params.append(pts_id)

        query = " ".join(query_parts)
        cursor.execute(query, params)
        results = cursor.fetchall()

        if not results:
            return ""

        # 将结果格式化为 'value1', 'value2', 'value3'
        formatted_results = ", ".join(f"'{result[0]}'" for result in results)
        return formatted_results

    except Exception as e:
        logging.error(f"获取就诊信息出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, True)


def get_biz_time_column(db_connector: DatabaseConnector, table_code: str) -> str:
    """获取业务时间列"""
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_direct_connection()
        cursor = conn.cursor()

        query = """
        SELECT column_code
        FROM ilink_business_columns
        WHERE table_code = %s
        AND column_type = 1
        AND is_delete = 0
        LIMIT 1
        """

        cursor.execute(query, (table_code,))
        result = cursor.fetchone()

        if not result:
            return ""

        return result[0]

    except Exception as e:
        logging.error(f"获取业务时间列出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, False)


def get_biz_status_condition(db_connector: DatabaseConnector, table_code: str) -> str:
    """获取业务状态条件"""
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_direct_connection()
        cursor = conn.cursor()

        query = """
        SELECT column_code, column_value
        FROM ilink_business_columns
        WHERE table_code = %s
        AND column_type = 2
        """

        cursor.execute(query, (table_code,))
        results = cursor.fetchall()

        if not results:
            return "1=1"  # 默认条件，始终为真

        # 按列名分组
        column_values_map = {}
        for column_code, column_value in results:
            if column_code not in column_values_map:
                column_values_map[column_code] = []
            column_values_map[column_code].append(column_value)

        # 构建条件语句
        conditions = []
        for column_code, values in column_values_map.items():
            formatted_values = ", ".join(f"'{value}'" for value in values)
            conditions.append(f"{column_code} IN ({formatted_values})")

        return " AND ".join(conditions)

    except Exception as e:
        logging.error(f"获取业务状态条件出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, False)


def execute_main_query(
        db_connector: DatabaseConnector,
        table_code: str,
        sub_node_visit: str,
        biz_time_column: str,
        biz_status_condition: str,
        hospital_no: str,
        pro_id: str,
        time_type: Optional[int] = None,
        biz_start_time: Optional[str] = None,
        biz_end_time: Optional[str] = None
) -> List[Dict]:
    """
    执行主查询

    Args:
        db_connector: 数据库连接器
        table_code: 表代码
        sub_node_visit: 就诊信息
        biz_time_column: 业务时间列
        biz_status_condition: 业务状态条件
        hospital_no: 医院编号
        pro_id: 项目ID
        time_type: 时间类型 (1: 最近一次, 2: 时间范围, 3: 大于等于, 4: 小于等于, 5: 时间点)
        biz_start_time: 业务开始时间
        biz_end_time: 业务结束时间
    """
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_dr_connection()
        cursor = conn.cursor()

        # 构建基础查询
        base_query = f"""
        SELECT *
        FROM {table_code}
        WHERE concat(visit_id, visit_type) IN ({sub_node_visit})
        AND hospital_no = %s
        AND pro_id = %s
        AND ({biz_status_condition})
        """

        params = [hospital_no, pro_id]

        # 根据时间类型添加不同的时间条件
        if time_type == 1:  # 最近一次
            # 为每个就诊ID获取最近一次记录
            query = f"""
            WITH ranked_records AS (
                SELECT *,
                       ROW_NUMBER() OVER(PARTITION BY concat(visit_id, visit_type)
                                         ORDER BY {biz_time_column} DESC) as rn
                FROM {table_code}
                WHERE concat(visit_id, visit_type) IN ({sub_node_visit})
                AND hospital_no = %s
                AND pro_id = %s
                AND ({biz_status_condition})
            )
            SELECT * FROM ranked_records WHERE rn = 1
            """
        elif time_type == 2:  # 时间范围
            query = base_query

            # 只有在同时提供开始和结束时间时才添加BETWEEN条件
            if biz_start_time and biz_end_time:
                # 检查开始和结束时间是否相同且只有日期部分（不包含时间）
                is_same_date_only = (biz_start_time == biz_end_time and
                                    len(biz_start_time) <= 10 and ":" not in biz_start_time)

                if is_same_date_only:
                    # 如果是同一天且只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=2(时间范围)查询结构化数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    query += f" AND ({biz_time_column} BETWEEN %s AND %s)"
                    params.extend([biz_start_time, biz_end_time])
                    logging.info(f"使用time_type=2(时间范围)查询结构化数据: {biz_start_time} 至 {biz_end_time}")
            # 否则，根据提供的参数添加单独的条件
            elif biz_start_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=2(时间范围)查询结构化数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    query += f" AND ({biz_time_column} >= %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=2(时间范围)查询结构化数据: 大于等于 {biz_start_time}")
            elif biz_end_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_end_time) <= 10 and ":" not in biz_end_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_end_time} 00:00:00")
                    params.append(f"{biz_end_time} 23:59:59")
                    logging.info(f"使用time_type=2(时间范围)查询结构化数据: {biz_end_time}，范围从 00:00:00 到 23:59:59")
                else:
                    query += f" AND ({biz_time_column} <= %s)"
                    params.append(biz_end_time)
                    logging.info(f"使用time_type=2(时间范围)查询结构化数据: 小于等于 {biz_end_time}")

        elif time_type == 3:  # 单一时间点 - 大于等于
            query = base_query
            if biz_start_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，使用该天的开始时间（00:00:00）
                    query += f" AND ({biz_time_column} >= %s)"
                    params.append(f"{biz_start_time} 00:00:00")
                    logging.info(f"使用time_type=3(大于等于)查询结构化数据: {biz_start_time} 00:00:00")
                else:
                    # 如果包含时间部分，使用原始时间
                    query += f" AND ({biz_time_column} >= %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=3(大于等于)查询结构化数据: {biz_start_time}")

        elif time_type == 4:  # 单一时间点 - 小于等于
            query = base_query
            if biz_end_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_end_time) <= 10 and ":" not in biz_end_time

                if is_date_only:
                    # 如果只有日期，使用该天的结束时间（23:59:59）
                    query += f" AND ({biz_time_column} <= %s)"
                    params.append(f"{biz_end_time} 23:59:59")
                    logging.info(f"使用time_type=4(小于等于)查询结构化数据: {biz_end_time} 23:59:59")
                else:
                    # 如果包含时间部分，使用原始时间
                    query += f" AND ({biz_time_column} <= %s)"
                    params.append(biz_end_time)
                    logging.info(f"使用time_type=4(小于等于)查询结构化数据: {biz_end_time}")

        elif time_type == 5:  # 时间点 - 精确匹配或日期范围
            query = base_query
            # 如果提供了业务开始时间
            if biz_start_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=5(日期)查询结构化数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    # 如果包含时间部分，使用精确匹配
                    query += f" AND ({biz_time_column} = %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=5(时间点)查询结构化数据: {biz_start_time}")
            # 如果同时提供了业务开始和结束时间，且它们相同，也视为时间点
            elif biz_start_time and biz_end_time and biz_start_time == biz_end_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=5(日期)查询结构化数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    # 如果包含时间部分，使用精确匹配
                    query += f" AND ({biz_time_column} = %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=5(时间点)查询结构化数据: {biz_start_time}")
            else:
                logging.info(f"使用time_type=5(时间点)查询结构化数据，但未提供有效的时间参数")

        else:  # 默认情况，不添加时间条件
            query = base_query

        cursor.execute(query, params)
        columns = [desc[0] for desc in cursor.description]
        results = cursor.fetchall()

        # 将结果转换为字典列表
        result_dicts = []
        for row in results:
            result_dict = {}
            for i, column in enumerate(columns):
                # 处理datetime类型，转换为ISO格式字符串
                if isinstance(row[i], datetime):
                    result_dict[column] = row[i].isoformat()
                else:
                    result_dict[column] = row[i]
            result_dicts.append(result_dict)

        return result_dicts

    except Exception as e:
        logging.error(f"执行主查询出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, True)


# 自定义JSON编码器，处理datetime等类型
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super(DateTimeEncoder, self).default(obj)


def transform_entity(data: List[Dict], api_url: str = "/iLink/transform-entity/structural") -> Dict:
    """
    将查询结果发送到 /iLink/transform-entity 接口进行处理

    Args:
        data: 需要转换的数据列表
        api_url: API URL，默认为 "/iLink/transform-entity"

    Returns:
        API响应结果
    """
    try:
        if not data:
            logging.warning("没有数据需要转换")
            return {"success": False, "message": "No data to transform", "data": None}

        # 设置默认API URL
        if not api_url.startswith(("http://", "https://")):
            # 如果只提供了路径，添加默认的域名
            base_url = "http://localhost:8080"  # 默认域名，可以根据实际情况修改
            api_url = f"{base_url}{api_url if api_url.startswith('/') else '/' + api_url}"

        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # 使用自定义编码器处理datetime对象，确保JSON序列化正确
        json_data = json.dumps(data, cls=DateTimeEncoder)

        # 发送POST请求
        response = requests.post(
            url=api_url,
            headers=headers,
            data=json_data,
            timeout=60  # 设置超时时间为60秒
        )

        # 检查响应状态
        response.raise_for_status()

        # 解析响应结果
        result = response.json()

        return result

    except requests.exceptions.RequestException as e:
        logging.error(f"API请求出错: {str(e)}")
        return {"success": False, "message": str(e), "data": None}
    except json.JSONDecodeError as e:
        logging.error(f"解析API响应出错: {str(e)}")
        return {"success": False, "message": f"Failed to parse API response: {str(e)}", "data": None}
    except Exception as e:
        logging.error(f"转换实体出错: {str(e)}")
        return {"success": False, "message": str(e), "data": None}


# 工作流函数入口
def workflow_function(
        table_code: str,
        empi_id: str,
        pts_id: str,
        pro_id: str,
        hospital_no: str,
        time_type: Optional[int] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        biz_start_time: Optional[str] = None,
        biz_end_time: Optional[str] = None,
        transform_api_url: str = os.getenv("DEFAULT_TRANSFORM_API_URL")
) -> Dict:
    """
    工作流函数入口

    Args:
        table_code: 表代码
        empi_id: 患者EMPI ID (必需)
        pts_id: PTS ID (必需)
        pro_id: 项目ID (必需)
        hospital_no: 医院编号 (必需)
        time_type: 时间类型 (可选, 1: 最近一次, 2: 时间范围, 3: 大于等于, 4: 小于等于, 5: 时间点)
        start_time: 开始时间 (可选) - 用于就诊时间范围
        end_time: 结束时间 (可选) - 用于就诊时间范围
        biz_start_time: 业务开始时间 (可选) - 用于业务数据时间范围
        biz_end_time: 业务结束时间 (可选) - 用于业务数据时间范围
        transform_api_url: 转换API的URL (可选)

    Returns:
        处理结果字典，包含查询结果和转换结果
    """
    # 验证必需参数
    if not empi_id:
        raise ValueError("empi_id 是必需参数")
    if not pts_id:
        raise ValueError("pts_id 是必需参数")
    if not pro_id:
        raise ValueError("pro_id 是必需参数")
    if not hospital_no:
        raise ValueError("hospital_no 是必需参数")
    if not table_code:
        raise ValueError("table_code 是必需参数")

        # 如果未提供API URL，则使用环境变量中的默认值
    if transform_api_url is None:
        transform_api_url = os.getenv("DEFAULT_TRANSFORM_API_URL")

    db_connector = None
    try:
        db_connector = DatabaseConnector()

        # 1. 执行数据库查询获取原始数据
        query_results = execute_workflow_query(
            db_connector,
            table_code,
            hospital_no,
            pro_id,
            empi_id,
            pts_id,
            time_type,
            start_time,
            end_time,
            biz_start_time,
            biz_end_time
        )

        # 2. 如果有查询结果，发送到转换API
        if query_results:
            # 调用transform-entity API进行数据转换
            formatted_data = {table_code: query_results}
            source_entities = {"sourceEntities":formatted_data}
            transform_result = transform_entity(
                data=source_entities,
                api_url=transform_api_url
            )

            # 3. 返回完整结果
            return {
                "success": True,
                "message": "数据处理成功",
                "visits": transform_result,
                "transform_results": transform_result
            }
        else:
            return {
                "success": False,
                "message": "未找到符合条件的数据",
                "visits": [],
                "transform_results": None
            }

    except Exception as e:
        logging.error(f"工作流执行出错: {str(e)}")
        return {
            "success": False,
            "message": str(e),
            "visits": None,
            "transform_results": None
        }
    finally:
        if db_connector:
            db_connector.close_all_connections()


if __name__ == '__main__':
    # 设置基本日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 示例调用
    result = workflow_function(
        table_code="vts_vital_sign_items",
        empi_id="07bcb00a33f8d99e6d5b030081c64b34",
        pts_id="300043000400144001",
        pro_id="30004",
        hospital_no="123301104703906486"
    )

    # 使用自定义编码器打印结果，确保datetime对象正确序列化
    print(json.dumps(result, indent=2, ensure_ascii=False, cls=DateTimeEncoder))
