import psycopg2
from psycopg2 import pool
import logging
import requests
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import os
import re
from dotenv import load_dotenv

load_dotenv()


class DatabaseConnector:
    def __init__(self):
        # 创建数据库连接池
        self.db_prime_dr_pool = psycopg2.pool.SimpleConnectionPool(
            1, 10,
            host=os.getenv("DB_PRIME_DR_HOST"),
            database=os.getenv("DB_PRIME_DR_DATABASE"),
            user=os.getenv("DB_PRIME_DR_USER"),
            password=os.getenv("DB_PRIME_DR_PASSWORD"),
            port=os.getenv("DB_PRIME_DR_PORT"),
            options=f"-c search_path={os.getenv('DB_PRIME_DR_SCHEMA')}"
        )

        self.db_prime_direct_pool = psycopg2.pool.SimpleConnectionPool(
            1, 10,
            host=os.getenv("DB_PRIME_DIRECT_HOST"),
            database=os.getenv("DB_PRIME_DIRECT_DATABASE"),
            user=os.getenv("DB_PRIME_DIRECT_USER"),
            password=os.getenv("DB_PRIME_DIRECT_PASSWORD"),
            port=os.getenv("DB_PRIME_DIRECT_PORT"),
            options=f"-c search_path={os.getenv('DB_PRIME_DIRECT_SCHEMA')}"
        )

        self.logger = logging.getLogger(__name__)

    def get_db_prime_dr_connection(self):
        return self.db_prime_dr_pool.getconn()

    def get_db_prime_direct_connection(self):
        return self.db_prime_direct_pool.getconn()

    def release_connection(self, conn, is_prime_dr=True):
        if is_prime_dr:
            self.db_prime_dr_pool.putconn(conn)
        else:
            self.db_prime_direct_pool.putconn(conn)

    def close_all_connections(self):
        if self.db_prime_dr_pool:
            self.db_prime_dr_pool.closeall()
        if self.db_prime_direct_pool:
            self.db_prime_direct_pool.closeall()


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super(DateTimeEncoder, self).default(obj)


def extract_visit_name_from_text(text: str) -> Optional[str]:
    """
    从自然语言文本中提取访视名称
    例如：从"帮我从基线访视的所有纸质文件中提取信息"中提取"基线访视"
    """
    # 常见的访视名称模式
    visit_patterns = [
        r'(基线访视)',
        r'(筛选访视)',
        r'(随访访视)',
        r'(第\d+次访视)',
        r'(\d+周访视)',
        r'(\d+月访视)',
        r'(随机化访视)',
        r'(终止访视)',
        r'(结束访视)'
    ]

    for pattern in visit_patterns:
        match = re.search(pattern, text)
        if match:
            return match.group(1)

    return None


def get_pro_visit_id(db_connector: DatabaseConnector, visit_name: str, pro_id: str, design_version_id: str) -> Optional[
    str]:
    """
    根据访视名称获取pro_visit_id
    """
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_direct_connection()
        cursor = conn.cursor()

        query = """
        SELECT id FROM pro_visit
        WHERE name LIKE %s
        AND pro_id = %s
        AND design_version_id = %s
        AND is_delete = 0
        LIMIT 1
        """

        cursor.execute(query, (f"%{visit_name}%", pro_id, design_version_id))
        result = cursor.fetchone()

        if result:
            return result[0]
        return None

    except Exception as e:
        logging.error(f"获取pro_visit_id出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, False)


def get_source_file_info(db_connector: DatabaseConnector, pro_visit_id: str, pts_id: str) -> List[str]:
    """
    获取source_file_info表中的数据
    """
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_direct_connection()
        cursor = conn.cursor()

        query = """
        SELECT id FROM source_file_info
        WHERE pts_id = %s
        AND pro_visit_id = %s
        AND is_delete = 0
        """

        cursor.execute(query, (pts_id, pro_visit_id))
        results = cursor.fetchall()

        return [str(result[0]) for result in results]

    except Exception as e:
        logging.error(f"获取source_file_info出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, False)


def get_paper_results(
    db_connector: DatabaseConnector,
    source_file_ids: List[str],
    pts_id: str,
    time_type: Optional[int] = None,
    biz_time_column: Optional[str] = "create_time",  # 默认使用create_time作为业务时间列
    biz_start_time: Optional[str] = None,
    biz_end_time: Optional[str] = None
) -> List[Dict]:
    """
    获取ilink_paper_results表中的数据

    Args:
        db_connector: 数据库连接器
        source_file_ids: 源文件ID列表
        pts_id: 患者ID
        time_type: 时间类型 (可选, 1: 最近一次, 2: 时间范围, 3: 大于等于, 4: 小于等于, 5: 时间点)
        biz_time_column: 业务时间列名 (默认为create_time)
        biz_start_time: 业务开始时间 (可选)
        biz_end_time: 业务结束时间 (可选)

    Returns:
        查询结果列表
    """
    if not source_file_ids:
        return []

    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_direct_connection()
        cursor = conn.cursor()

        # 构建IN子句
        source_file_ids_str = ", ".join([f"'{id}'" for id in source_file_ids])

        # 构建基础查询
        base_query = f"""
        SELECT * FROM ilink_paper_results
        WHERE source_file_id IN ({source_file_ids_str})
        AND pts_id = %s
        """

        params = [pts_id]

        # 根据时间类型添加不同的时间条件
        if time_type == 1:  # 最近一次
            # 为每个source_file_id获取最近一次记录
            query = f"""
            WITH ranked_records AS (
                SELECT *,
                       ROW_NUMBER() OVER(PARTITION BY source_file_id
                                         ORDER BY {biz_time_column} DESC) as rn
                FROM ilink_paper_results
                WHERE source_file_id IN ({source_file_ids_str})
                AND pts_id = %s
            )
            SELECT * FROM ranked_records WHERE rn = 1
            """
            logging.info(f"使用time_type=1(最近一次)查询纸质文件数据")
        elif time_type == 2:  # 时间范围
            query = base_query

            # 只有在同时提供开始和结束时间时才添加BETWEEN条件
            if biz_start_time and biz_end_time:
                # 检查开始和结束时间是否相同且只有日期部分（不包含时间）
                is_same_date_only = (biz_start_time == biz_end_time and
                                    len(biz_start_time) <= 10 and ":" not in biz_start_time)

                if is_same_date_only:
                    # 如果是同一天且只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=2(时间范围)查询纸质文件数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    query += f" AND ({biz_time_column} BETWEEN %s AND %s)"
                    params.extend([biz_start_time, biz_end_time])
                    logging.info(f"使用time_type=2(时间范围)查询纸质文件数据: {biz_start_time} 至 {biz_end_time}")
            # 否则，根据提供的参数添加单独的条件
            elif biz_start_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=2(时间范围)查询纸质文件数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    query += f" AND ({biz_time_column} >= %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=2(时间范围)查询纸质文件数据: 大于等于 {biz_start_time}")
            elif biz_end_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_end_time) <= 10 and ":" not in biz_end_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_end_time} 00:00:00")
                    params.append(f"{biz_end_time} 23:59:59")
                    logging.info(f"使用time_type=2(时间范围)查询纸质文件数据: {biz_end_time}，范围从 00:00:00 到 23:59:59")
                else:
                    query += f" AND ({biz_time_column} <= %s)"
                    params.append(biz_end_time)
                    logging.info(f"使用time_type=2(时间范围)查询纸质文件数据: 小于等于 {biz_end_time}")
            else:
                logging.info(f"使用time_type=2(时间范围)查询纸质文件数据，但未提供时间参数")

        elif time_type == 3:  # 单一时间点 - 大于等于
            query = base_query
            if biz_start_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，使用该天的开始时间（00:00:00）
                    query += f" AND ({biz_time_column} >= %s)"
                    params.append(f"{biz_start_time} 00:00:00")
                    logging.info(f"使用time_type=3(大于等于)查询纸质文件数据: {biz_start_time} 00:00:00")
                else:
                    # 如果包含时间部分，使用原始时间
                    query += f" AND ({biz_time_column} >= %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=3(大于等于)查询纸质文件数据: {biz_start_time}")
            else:
                logging.info(f"使用time_type=3(大于等于)查询纸质文件数据，但未提供时间参数")

        elif time_type == 4:  # 单一时间点 - 小于等于
            query = base_query
            if biz_end_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_end_time) <= 10 and ":" not in biz_end_time

                if is_date_only:
                    # 如果只有日期，使用该天的结束时间（23:59:59）
                    query += f" AND ({biz_time_column} <= %s)"
                    params.append(f"{biz_end_time} 23:59:59")
                    logging.info(f"使用time_type=4(小于等于)查询纸质文件数据: {biz_end_time} 23:59:59")
                else:
                    # 如果包含时间部分，使用原始时间
                    query += f" AND ({biz_time_column} <= %s)"
                    params.append(biz_end_time)
                    logging.info(f"使用time_type=4(小于等于)查询纸质文件数据: {biz_end_time}")
            else:
                logging.info(f"使用time_type=4(小于等于)查询纸质文件数据，但未提供时间参数")

        elif time_type == 5:  # 时间点 - 精确匹配或日期范围
            query = base_query
            # 如果提供了业务开始时间
            if biz_start_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=5(日期)查询纸质文件数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    # 如果包含时间部分，使用精确匹配
                    query += f" AND ({biz_time_column} = %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=5(时间点)查询纸质文件数据: {biz_start_time}")
            # 如果同时提供了业务开始和结束时间，且它们相同，也视为时间点
            elif biz_start_time and biz_end_time and biz_start_time == biz_end_time:
                # 检查是否只有日期部分（不包含时间）
                is_date_only = len(biz_start_time) <= 10 and ":" not in biz_start_time

                if is_date_only:
                    # 如果只有日期，将查询范围设置为该天的开始到结束
                    query += f" AND ({biz_time_column} >= %s AND {biz_time_column} <= %s)"
                    # 添加当天开始时间（00:00:00）和结束时间（23:59:59）
                    params.append(f"{biz_start_time} 00:00:00")
                    params.append(f"{biz_start_time} 23:59:59")
                    logging.info(f"使用time_type=5(日期)查询纸质文件数据: {biz_start_time}，范围从 00:00:00 到 23:59:59")
                else:
                    # 如果包含时间部分，使用精确匹配
                    query += f" AND ({biz_time_column} = %s)"
                    params.append(biz_start_time)
                    logging.info(f"使用time_type=5(时间点)查询纸质文件数据: {biz_start_time}")
            else:
                logging.info(f"使用time_type=5(时间点)查询纸质文件数据，但未提供有效的时间参数")

        else:  # 默认情况，不添加时间条件
            query = base_query
            logging.info(f"未指定time_type，使用默认查询纸质文件数据")

        cursor.execute(query, params)
        columns = [desc[0] for desc in cursor.description]
        results = cursor.fetchall()

        # 将结果转换为字典列表
        result_dicts = []
        for row in results:
            result_dict = {}
            for i, column in enumerate(columns):
                if isinstance(row[i], datetime):
                    result_dict[column] = row[i].isoformat()
                else:
                    result_dict[column] = row[i]
            result_dicts.append(result_dict)

        logging.info(f"查询到 {len(result_dicts)} 条纸质文件数据记录")
        return result_dicts

    except Exception as e:
        logging.error(f"获取paper_results出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, False)


def transform_paper_entity(data: List[Dict],pro_visit_id: str, api_url: str = "/iLink/transform-entity/paper") -> Dict:
    """
    将查询结果发送到 /iLink/transform-entity/paper 接口进行处理

    Args:
        data: 需要转换的数据列表
        api_url: API URL

    Returns:
        API响应结果
    """
    try:
        if not data:
            logging.warning("没有数据需要转换")
            return {"success": False, "message": "No data to transform", "data": None}

        # 设置默认API URL
        if not api_url.startswith(("http://", "https://")):
            base_url = "http://localhost:8080"
            api_url = f"{base_url}{api_url if api_url.startswith('/') else '/' + api_url}"

        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        results = {"ilink_paper_results": data}
        # 准备请求数据
        request_data = {
            "sourceEntities": results,
            "proVisitId": pro_visit_id
        }

        # 使用自定义编码器处理datetime对象
        json_data = json.dumps(request_data, cls=DateTimeEncoder)

        # 发送POST请求
        response = requests.post(
            url=api_url,
            headers=headers,
            data=json_data,
            timeout=60
        )

        # 检查响应状态
        response.raise_for_status()

        # 解析响应结果
        result = response.json()

        return result

    except requests.exceptions.RequestException as e:
        logging.error(f"API请求出错: {str(e)}")
        return {"success": False, "message": str(e), "data": None}
    except json.JSONDecodeError as e:
        logging.error(f"解析API响应出错: {str(e)}")
        return {"success": False, "message": f"Failed to parse API response: {str(e)}", "data": None}
    except Exception as e:
        logging.error(f"转换实体出错: {str(e)}")
        return {"success": False, "message": str(e), "data": None}


def paper_entity_workflow(
        natural_language_text: str,
        pts_id: str,
        pro_id: str,
        design_version_id: str,
        table_code: str,
        time_type: Optional[int] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        biz_start_time: Optional[str] = None,
        biz_end_time: Optional[str] = None,
        transform_api_url: str = None
) -> Dict:
    """
    纸质文件实体提取工作流

    Args:
        natural_language_text: 自然语言文本，用于提取访视信息
        pts_id: 患者ID
        pro_id: 项目ID
        design_version_id: 设计版本ID
        table_code: 表代码
        time_type: 时间类型 (可选, 1: 最近一次, 2: 时间范围, 3: 大于等于, 4: 小于等于, 5: 时间点)
        start_time: 开始时间 (可选) - 用于就诊时间范围
        end_time: 结束时间 (可选) - 用于就诊时间范围
        biz_start_time: 业务开始时间 (可选) - 用于业务数据时间范围
        biz_end_time: 业务结束时间 (可选) - 用于业务数据时间范围
        transform_api_url: 转换API的URL (可选)

    Returns:
        处理结果字典
    """
    # 验证必需参数
    if not pts_id:
        raise ValueError("pts_id 是必需参数")
    if not pro_id:
        raise ValueError("pro_id 是必需参数")
    if not design_version_id:
        raise ValueError("design_version_id 是必需参数")
    if not table_code:
        raise ValueError("table_code 是必需参数")

    # 记录时间参数
    logging.info(f"纸质文件实体提取工作流参数: time_type={time_type}, start_time={start_time}, end_time={end_time}, "
                 f"biz_start_time={biz_start_time}, biz_end_time={biz_end_time}")

    db_connector = None
    try:
        db_connector = DatabaseConnector()

        # 1. 从自然语言中提取访视名称
        visit_name = extract_visit_name_from_text(natural_language_text)
        logging.info(f"从自然语言中提取的访视名称: {visit_name}")

        # 2. 判断CRC是否指定了访视
        if visit_name:
            # 3. 如果指定了访视，获取pro_visit_id
            pro_visit_id = get_pro_visit_id(db_connector, visit_name, pro_id, design_version_id)
            logging.info(f"获取到的pro_visit_id: {pro_visit_id}")

            if not pro_visit_id:
                logging.warning(f"未找到名称为 '{visit_name}' 的访视")
                return {
                    "success": False,
                    "message": f"未找到名称为 '{visit_name}' 的访视",
                    "data": None
                }

            # 4. 获取source_file_info
            source_file_ids = get_source_file_info(db_connector, pro_visit_id, pts_id)
            logging.info(f"获取到 {len(source_file_ids)} 个source_file_ids")

        else:
            # 如果没有指定访视，尝试获取所有访视
            logging.warning("未能从自然语言中提取访视信息，尝试获取所有访视")

            # 获取患者的所有访视
            try:
                conn = db_connector.get_db_prime_direct_connection()
                cursor = conn.cursor()

                # 查询患者的所有访视
                query = """
                SELECT id FROM pro_visit
                WHERE pro_id = %s
                AND design_version_id = %s
                AND is_delete = 0
                """

                cursor.execute(query, (pro_id, design_version_id))
                visit_results = cursor.fetchall()

                if not visit_results:
                    logging.warning(f"未找到患者 {pts_id} 的任何访视")
                    return {
                        "success": False,
                        "message": "未找到患者的任何访视信息",
                        "data": None
                    }

                # 获取所有访视的source_file_ids
                source_file_ids = []
                pro_visit_id = None

                for visit_result in visit_results:
                    visit_id = visit_result[0]
                    # 如果还没有设置pro_visit_id，使用第一个找到的
                    if pro_visit_id is None:
                        pro_visit_id = visit_id

                    # 获取当前访视的source_file_ids
                    visit_source_file_ids = get_source_file_info(db_connector, visit_id, pts_id)
                    source_file_ids.extend(visit_source_file_ids)

                logging.info(f"从所有访视中获取到 {len(source_file_ids)} 个source_file_ids")

                if not source_file_ids:
                    logging.warning(f"未找到患者 {pts_id} 的任何纸质文件")
                    return {
                        "success": False,
                        "message": "未找到患者的任何纸质文件",
                        "data": None
                    }

            except Exception as e:
                logging.error(f"获取所有访视出错: {str(e)}")
                return {
                    "success": False,
                    "message": f"获取访视信息出错: {str(e)}",
                    "data": None
                }
            finally:
                if cursor:
                    cursor.close()
                if conn:
                    db_connector.release_connection(conn, False)

        # 5. 获取ilink_paper_results，添加时间类型和时间范围参数
        paper_results = get_paper_results(
            db_connector,
            source_file_ids,
            pts_id,
            time_type=time_type,
            biz_start_time=biz_start_time,
            biz_end_time=biz_end_time
        )

        if not paper_results:
            logging.warning(f"未找到符合条件的纸质文件数据")
            return {
                "success": False,
                "message": "未找到符合条件的纸质文件数据",
                "data": None
            }

        # 设置默认的transform_api_url
        if transform_api_url is None:
            transform_api_url = os.getenv("DEFAULT_TRANSFORM_PAPER_API_URL", "http://192.168.1.67:9000/api/iLink/transform-entity/paper")
            logging.info(f"使用默认的transform_api_url: {transform_api_url}")

        # 6. 调用transform-entity API进行数据转换
        transform_result = transform_paper_entity(
            data=paper_results,
            pro_visit_id=pro_visit_id,
            api_url=transform_api_url
        )

        # 7. 返回完整结果
        return {
            "success": True,
            "message": "纸质文件数据处理成功",
            "paper_results": paper_results,
            "transform_results": transform_result,
            "visits": transform_result  # 添加visits字段，与structure.py保持一致
        }

    except Exception as e:
        logging.error(f"纸质文件实体提取工作流执行出错: {str(e)}")
        return {
            "success": False,
            "message": str(e),
            "data": None
        }
    finally:
        if db_connector:
            db_connector.close_all_connections()


# 对外暴露的API函数
def transform_paper_entity_api(param: Dict) -> Dict:
    """
    对外暴露的API函数，处理纸质文件实体提取

    Args:
        param: NavigatorTransformParam参数
        {
            "naturalLanguage": "帮我从基线访视的所有纸质文件中提取信息",
            "pts_id": "300043000400144001",
            "pro_id": "30004",
            "design_version_id": "12345",
            "table_code": "ilink_paper_results",
            "time_type": 1,  # 可选，时间类型 (1: 最近一次, 2: 时间范围, 3: 大于等于, 4: 小于等于, 5: 时间点)
            "start_time": "2023-01-01",  # 可选，开始时间
            "end_time": "2023-12-31",  # 可选，结束时间
            "biz_start_time": "2023-01-01",  # 可选，业务开始时间
            "biz_end_time": "2023-12-31"  # 可选，业务结束时间
        }

    Returns:
        处理结果
    """
    try:
        # 记录输入参数
        logging.info(f"transform_paper_entity_api接收到的参数: {param}")

        # 获取必要参数
        natural_language = param.get("naturalLanguage", "")
        pts_id = param.get("pts_id")
        pro_id = param.get("pro_id")
        design_version_id = param.get("design_version_id")
        table_code = "ilink_paper_results"

        # 获取时间相关参数
        time_type = param.get("time_type")
        start_time = param.get("start_time")
        end_time = param.get("end_time")
        biz_start_time = param.get("biz_start_time")
        biz_end_time = param.get("biz_end_time")

        # 参数校验和日志记录
        if not pts_id:
            logging.warning("缺少必要参数pts_id")
        if not pro_id:
            logging.warning("缺少必要参数pro_id")
        if not design_version_id:
            logging.warning("缺少必要参数design_version_id")
        if not table_code:
            logging.warning("缺少必要参数table_code")

        # 记录时间参数
        if time_type is not None:
            logging.info(f"使用time_type={time_type}处理纸质文件数据")

            # 根据time_type记录相应的时间参数
            if time_type == 1:  # 最近一次
                logging.info("使用最近一次模式查询纸质文件数据")
            elif time_type == 2:  # 时间范围
                logging.info(f"使用时间范围模式查询纸质文件数据: biz_start_time={biz_start_time}, biz_end_time={biz_end_time}")
            elif time_type == 3:  # 大于等于
                logging.info(f"使用大于等于模式查询纸质文件数据: biz_start_time={biz_start_time}")
            elif time_type == 4:  # 小于等于
                logging.info(f"使用小于等于模式查询纸质文件数据: biz_end_time={biz_end_time}")
            elif time_type == 5:  # 时间点
                logging.info(f"使用时间点模式查询纸质文件数据: biz_start_time={biz_start_time}")
        else:
            logging.info("未指定time_type，使用默认模式查询纸质文件数据")

        # 调用工作流函数
        result = paper_entity_workflow(
            natural_language_text=natural_language,
            pts_id=pts_id,
            pro_id=pro_id,
            design_version_id=design_version_id,
            table_code=table_code
        )

        # 记录处理结果
        if result.get("success"):
            logging.info(f"纸质文件数据处理成功: {result.get('message')}")
        else:
            logging.warning(f"纸质文件数据处理失败: {result.get('message')}")

        return result

    except Exception as e:
        logging.error(f"API函数执行出错: {str(e)}")
        return {
            "success": False,
            "message": str(e),
            "data": None
        }


if __name__ == '__main__':
    # 设置基本日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 示例调用
    param = {
        "naturalLanguage": "帮我从基线访视的所有纸质文件中提取信息",
        "pts_id": "300043000400444003",
        "pro_id": "30004",
        "design_version_id": "8",
        "pro_visit_id": "70",
        "table_code": "ilink_paper_results"
    }

    result = transform_paper_entity_api(param)

    # 使用自定义编码器打印结果
    print(json.dumps(result, indent=2, ensure_ascii=False, cls=DateTimeEncoder))
