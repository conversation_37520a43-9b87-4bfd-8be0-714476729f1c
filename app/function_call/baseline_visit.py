import psycopg2
from psycopg2 import pool
import logging
import json
from typing import Dict, Optional
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()


class DatabaseConnector:
    def __init__(self):
        # 创建数据库连接池
        self.db_prime_dr_pool = psycopg2.pool.SimpleConnectionPool(
            1, 10,
            host=os.getenv("DB_PRIME_DR_HOST"),
            database=os.getenv("DB_PRIME_DR_DATABASE"),
            user=os.getenv("DB_PRIME_DR_USER"),
            password=os.getenv("DB_PRIME_DR_PASSWORD"),
            port=os.getenv("DB_PRIME_DR_PORT"),
            options=f"-c search_path={os.getenv('DB_PRIME_DR_SCHEMA')}"
        )

        self.db_prime_direct_pool = psycopg2.pool.SimpleConnectionPool(
            1, 10,
            host=os.getenv("DB_PRIME_DIRECT_HOST"),
            database=os.getenv("DB_PRIME_DIRECT_DATABASE"),
            user=os.getenv("DB_PRIME_DIRECT_USER"),
            password=os.getenv("DB_PRIME_DIRECT_PASSWORD"),
            port=os.getenv("DB_PRIME_DIRECT_PORT"),
            options=f"-c search_path={os.getenv('DB_PRIME_DIRECT_SCHEMA')}"
        )

        self.logger = logging.getLogger(__name__)

    def get_db_prime_dr_connection(self):
        return self.db_prime_dr_pool.getconn()

    def get_db_prime_direct_connection(self):
        return self.db_prime_direct_pool.getconn()

    def release_connection(self, conn, is_prime_dr=True):
        if is_prime_dr:
            self.db_prime_dr_pool.putconn(conn)
        else:
            self.db_prime_direct_pool.putconn(conn)

    def close_all_connections(self):
        if self.db_prime_dr_pool:
            self.db_prime_dr_pool.closeall()
        if self.db_prime_direct_pool:
            self.db_prime_direct_pool.closeall()


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super(DateTimeEncoder, self).default(obj)


def get_baseline_visit_date(db_connector: DatabaseConnector, pts_id: str) -> Optional[Dict]:
    """
    获取首次访视时间

    Args:
        db_connector: 数据库连接器
        pts_id: 患者ID

    Returns:
        包含首次访视时间的字典，如果未找到则返回None
    """
    conn = None
    cursor = None
    try:
        conn = db_connector.get_db_prime_direct_connection()
        cursor = conn.cursor()

        query = """
        SELECT baseline_visit_date FROM schema_icrc.pro_patient
        WHERE pts_id = %s AND is_delete = 0
        LIMIT 1
        """

        cursor.execute(query, (pts_id,))
        result = cursor.fetchone()

        if not result:
            logging.info(f"未找到患者 {pts_id} 的首次访视时间")
            return None

        baseline_visit_date = result[0]

        # 如果是datetime类型，转换为ISO格式字符串
        if isinstance(baseline_visit_date, datetime):
            baseline_visit_date = baseline_visit_date.isoformat()

        return {
            "baseline_visit_date": baseline_visit_date
        }

    except Exception as e:
        logging.error(f"获取首次访视时间出错: {str(e)}")
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            db_connector.release_connection(conn, False)


def baseline_visit_workflow(pts_id: str) -> Dict:
    """
    首次访视时间查询工作流

    Args:
        pts_id: 患者ID

    Returns:
        处理结果字典
    """
    # 验证必需参数
    if not pts_id:
        raise ValueError("pts_id 是必需参数")

    db_connector = None
    try:
        db_connector = DatabaseConnector()

        # 查询首次访视时间
        result = get_baseline_visit_date(db_connector, pts_id)

        if result:
            return {
                "success": True,
                "message": "成功获取首次访视时间",
                "data": result
            }
        else:
            return {
                "success": False,
                "message": f"未找到患者 {pts_id} 的首次访视时间",
                "data": None
            }

    except Exception as e:
        logging.error(f"首次访视时间查询工作流执行出错: {str(e)}")
        return {
            "success": False,
            "message": str(e),
            "data": None
        }
    finally:
        if db_connector:
            db_connector.close_all_connections()


# 对外暴露的API函数
def get_baseline_visit_date_api(param: Dict) -> Dict:
    """
    对外暴露的API函数，查询首次访视时间

    Args:
        param: 参数字典
        {
            "pts_id": "患者ID"
        }

    Returns:
        处理结果
    """
    try:
        pts_id = param.get("pts_id")

        if not pts_id:
            return {
                "success": False,
                "message": "缺少必需参数: pts_id",
                "data": None
            }

        result = baseline_visit_workflow(pts_id=pts_id)

        return result

    except Exception as e:
        logging.error(f"API函数执行出错: {str(e)}")
        return {
            "success": False,
            "message": str(e),
            "data": None
        }


if __name__ == '__main__':
    # 设置基本日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 示例调用
    param = {
        "pts_id": "300043000400144001"  # 替换为实际的患者ID
    }

    result = get_baseline_visit_date_api(param)

    # 使用自定义编码器打印结果
    print(json.dumps(result, indent=2, ensure_ascii=False, cls=DateTimeEncoder))
