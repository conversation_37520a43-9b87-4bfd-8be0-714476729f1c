"""
用户信息查询节点 - 负责查询用户信息
"""
import logging
from typing import Dict, Any, Optional

from app.domain.schemas import AssistantState
from app.core.executor import GenericToolExecutor

# 配置日志
logger = logging.getLogger(__name__)

def query_user_info(state: AssistantState, executor: GenericToolExecutor) -> Dict[str, Any]:
    """
    查询用户信息
    
    Args:
        state: 当前助手状态
        executor: 工具执行器实例
        
    Returns:
        包含更新状态的字典
    """
    logger.info("Executing query_user_info node")
    try:
        # 如果 state.user_name 为空，报错
        if not state.user_name:
            logger.error("User name is empty")
            return {"error_message": "User name is empty"}

        # 使用 executor 调用用户信息查询工具
        tool_result = executor.execute("query_user_info", {
            "name": state.user_name
        })
        logger.info(f"Raw query_user_info result: {tool_result}")
        
        # 处理用户信息查询结果
        if isinstance(tool_result, dict):
            # 如果是标准响应格式（包含 code、message、data）
            if "code" in tool_result and tool_result.get("code") == 0 and "data" in tool_result:
                user_info = tool_result.get("data")
                logger.info(f"Successfully got user info: {user_info}")
                return {"user_info_result": tool_result}
            # 如果是错误响应
            elif "code" in tool_result and tool_result.get("code") != 0:
                error_msg = tool_result.get("message", "Unknown error")
                logger.error(f"Tool returned error: {error_msg}")
                return {"error_message": error_msg}
        # 如果是直接返回的用户数据（不包含 code、message）
        elif isinstance(tool_result, dict) and not "code" in tool_result:
            user_info = tool_result
            logger.info(f"Successfully got user info: {user_info}")
            return {"user_info_result": {"code": 0, "message": "Success", "data": tool_result}}
            
        # 如果无法解析结果
        logger.error(f"Failed to parse user info from tool result: {tool_result}")
        return {"error_message": "Failed to execute or parse query_user_info tool."}
    except Exception as e:
        logger.error(f"Error executing query_user_info tool: {e}", exc_info=True)
        return {"error_message": f"Error calling query_user_info tool: {e}"}
