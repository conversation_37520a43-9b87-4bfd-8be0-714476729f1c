"""
响应生成节点 - 负责生成最终响应
"""
import json
import logging
from typing import Dict, Any, Optional
import os
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from app.domain.schemas import AssistantState

# 配置日志
logger = logging.getLogger(__name__)

# 最终响应提示模板
FINAL_RESPONSE_PROMPT_TEMPLATE = """
你是一个智能助手，你需要根据用户原始问题和工具调用返回的结构化数据，生成一个友好、清晰、自然的中文回复。

用户原始问题:
{query}

工具调用成功，返回的结构化数据如下 (JSON格式):
```json
{tool_data}
```

该工具预期返回数据的 Schema 定义如下 (JSON Schema格式) (如果 Schema 不可用，则显示 'Schema not available.'):
```json
{tool_schema}
```
如果 Schema 可用，请参考 Schema 中字段的 'description' 来理解数据含义。

请根据上述信息，生成最终的回复。请只返回回复内容，不要包含其他前缀或解释。
"""

# --- Start of Config Loading and Constants ---

# Path to tools_config.json (Relative to this file's directory)
CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                           "config", "tools_config.json")


TOOLS_MAP = {}
try:
    abs_config_path = os.path.abspath(CONFIG_PATH)
    logger.info(f"Attempting to load tools config from: {abs_config_path}")
    with open(abs_config_path, 'r', encoding='utf-8') as f:
        TOOLS_CONFIG = json.load(f)
    TOOLS_MAP = {tool['name']: tool for tool in TOOLS_CONFIG.get('tools', [])}
    logger.info(f"Successfully loaded tools config. Found {len(TOOLS_MAP)} tools.")
except FileNotFoundError:
    logger.error(f"Failed to load tools_config.json: File not found at {abs_config_path}")
except json.JSONDecodeError as e:
    logger.error(f"Failed to parse tools_config.json at {abs_config_path}: {e}")
except Exception as e:
    logger.error(f"An unexpected error occurred loading tools_config.json from {abs_config_path}: {e}")


def get_tool_output_schema(tool_name: str) -> Optional[Dict[str, Any]]:
    """Helper function to get the output schema for a given tool."""
   
    tool_config = TOOLS_MAP.get(tool_name)
    if not tool_config:
        logger.warning(f"Tool config not found for: {tool_name}")
        return None

    if tool_config['type'] == 'function':
        schema = tool_config.get('output_schema')
        logger.debug(f"Found function output schema for {tool_name}: {schema is not None}")
        return schema
    elif tool_config['type'] == 'rest':
        try:
            # Attempt to robustly find the 200 response schema
            paths = tool_config.get('openapi', {}).get('paths', {})
            if not paths: return None
            # Assuming only one path per tool definition for simplicity now
            path_key = list(paths.keys())[0]
            path_item = paths[path_key]

            # Find the first defined HTTP method (get, post, etc.)
            method_key = next((m for m in ['get', 'post', 'put', 'delete'] if m in path_item), None)
            if not method_key: return None
            method_item = path_item[method_key]

            # Extract schema from 200 response under application/json
            schema = method_item.get('responses', {}).get('200', {}).get('content', {}).get('application/json', {}).get(
                'schema')
            logger.debug(f"Found REST output schema for {tool_name}: {schema is not None}")
            return schema
        except (KeyError, IndexError, TypeError, AttributeError) as e:
            logger.warning(f"Could not extract OpenAPI response schema for tool '{tool_name}': {e}")
            return None
    logger.warning(f"Unknown tool type ('{tool_config.get('type')}') or schema extraction failed for: {tool_name}")
    return None

def generate_response(state: AssistantState, llm: ChatOpenAI) -> Dict[str, Any]:
    """
    根据查询结果和 Schema 生成最终响应
    
    Args:
        state: 当前助手状态
        llm: 语言模型实例
        
    Returns:
        包含最终响应的字典
    """
    logger.info("Executing generate_response node")
    
    # 如果有错误信息，直接返回错误
    if state.error_message:
        error_message = state.error_message
        logger.info(f"Returning error message: {error_message}")
        return {"final_response": f"抱歉，处理您的请求时出现了问题: {error_message}"}
    
    # 根据意图类型选择相应的结果和工具名称
    tool_result = None
    tool_name = None
    
    if state.intent == "district_query" and state.district_result:
        tool_result = state.district_result
        tool_name = "query_amap_district"
        logger.info("Generating response for district query")
    elif state.intent == "weather_query" and state.weather_result:
        tool_result = state.weather_result
        tool_name = "query_amap_weather"
        logger.info("Generating response for weather query")
    elif state.intent == "user_query" and state.user_info_result:
        tool_result = state.user_info_result
        tool_name = "query_user_info"
        logger.info("Generating response for user info query")
    elif state.intent == "time_query" and state.time_result:
        tool_result = state.time_result
        tool_name = "get_current_time"
        logger.info("Generating response for time query")
    else:
        # 如果没有匹配的意图或结果
        logger.warning(f"No matching intent or result found. Intent: {state.intent}")
        return {"final_response": "抱歉，我无法理解您的请求或找不到相关信息。请尝试用不同的方式提问。"}
    
    # 获取工具输出 Schema
    tool_schema = get_tool_output_schema(tool_name)
    tool_schema_str = json.dumps(tool_schema, ensure_ascii=False, indent=2) if tool_schema else "Schema not available."
    
    # 准备工具数据
    # 处理不同类型的工具结果
    if hasattr(tool_result, 'model_dump'):
        # Pydantic v2
        tool_data = tool_result.model_dump()
        tool_data_str = json.dumps(tool_data, ensure_ascii=False, indent=2)
    elif hasattr(tool_result, 'dict'):
        # Pydantic v1
        tool_data = tool_result.dict()
        tool_data_str = json.dumps(tool_data, ensure_ascii=False, indent=2)
    elif isinstance(tool_result, dict):
        # 已经是字典
        tool_data_str = json.dumps(tool_result, ensure_ascii=False, indent=2)
    elif isinstance(tool_result, str):
        # 字符串类型（如时间查询结果）
        tool_data_str = tool_result
    else:
        # 其他类型，尝试转换为字符串
        logger.warning(f"Unknown tool result type: {type(tool_result)}. Converting to string.")
        tool_data_str = str(tool_result)
    
    # 使用 LLM 生成最终响应
    try:
        prompt = ChatPromptTemplate.from_template(FINAL_RESPONSE_PROMPT_TEMPLATE)
        chain = prompt | llm
        
        result = chain.invoke({
            "query": state.query,
            "tool_data": tool_data_str,
            "tool_schema": tool_schema_str
        })
        
        final_response = result.content.strip()
        logger.info(f"Generated final response: {final_response}")
        
        return {"final_response": final_response}
    except Exception as e:
        logger.error(f"Error generating final response: {e}", exc_info=True)
        return {"final_response": f"抱歉，在生成回复时出现了问题: {str(e)}"}

def format_error_response(error_message: str) -> str:
    """
    格式化错误响应
    
    Args:
        error_message: 错误信息
        
    Returns:
        格式化后的错误响应
    """
    return f"抱歉，处理您的请求时出现了问题: {error_message}"
