# CRC/CRA 请求 AI 填写/重新填写表单 - 语料库与逻辑梳理

## 核心概念

1.  **主意图:**
    *   `Fill_Form`: 用户提供数据源（通常是刚上传的文件），要求基于此源填写表单。
2.  **子意图 (隶属于 `Fill_Form`，但逻辑特殊):**
    *   `Refill_Form`: 用户明确或隐含地要求 AI *忽略* 当前可能上传的文件，从其他来源（知识库、系统历史数据、特定时间范围等）获取信息来填写或重新填写表单。
3.  **数据来源判断 (针对 `Refill_Form`):**
    *   `Structure` (结构化): 默认来源。当用户未明确提及“病历”或“纸质”，或明确要求使用系统/数据库数据、历史数据、特定时间范围数据、最近数据等时触发。
    *   `Paper` (纸质): 当用户明确提及“纸质”、“打印”、“手写”、“扫描件”等相关词语时触发。
    *   `Medical_Record` (病历): 当用户明确提及“病历”、“病案”、“Source Document”、“源文件”等相关词语时触发。**（根据当前设定，此来源的 `Refill_Form` 请求将被标记为不支持）**

---

## Case 1: 单纯填写表单 (意图: `Fill_Form`)

*   **触发条件:** 用户提供数据源（如上传文件），并请求填写表单，*没有*使用“重新”、“重做”、“忽略”、“查找历史”、“特定时间”等触发 `Refill_Form` 的关键词。
*   **AI 行为:** 基于用户当前提供/上传的数据源进行填写。
*   **CRC/CRA 常用语料:**
    *   "请根据我刚上传的这份`[受试者编号XXX]`的`[文件类型，如：电子病历/访视记录]`，帮我填写`[表单类型，如：体温单/AE记录表]`。"
    *   "这是`[受试者编号YYY]`在`[访视Z]`的`[数据类型，如：生命体征/实验室检查]`记录（已上传），请提取`[具体数据项，如：体温/白细胞计数]`填到`[表单类型]`里。"
    *   "帮我处理下上传的文件，把里面的`[数据类型]`信息录入到标准`[表单类型]`模板。"
    *   "请基于提供的源文件，生成`[受试者编号AAA]`的`[表单类型]`。"
    *   "根据上传的`[文件类型]`，填写`[表单类型]`部分。"
    *   "把这个文件里的数据填到`[表单类型]`中。"
    *   "帮我录入一下这份上传资料对应的`[表单类型]`。"
    *   "帮我填写表单"

---

## Case 2: 重新填写表单 (意图: `Refill_Form`)

*   **触发条件:** 用户使用了“重新”、“重做”、“忽略上传文件”、“查找”、“调取”、“最近一次”、“特定时间”、“从...开始”、“根据系统/历史”等词语，暗示不使用当前上传的文件，而是从其他地方获取数据。
*   **AI 行为:** 首先判断数据来源子分类（`Structure`, `Paper`, `Medical_Record`），然后执行相应操作（或返回不支持）。

---

### Case 2.1: 结构化数据源 (Source: `Structure`)

*   **触发条件:** 属于 `Refill_Form` 意图，且用户*未*明确提及“病历”或“纸质”相关词语作为数据来源。这是 `Refill_Form` 的默认数据源。
*   **AI 行为:** 从知识库/数据库/系统历史记录中查找所需数据并填写表单。可能需要调用 Function Calling 获取特定时间范围或最新数据。
*   **CRC/CRA 常用语料:**

    *   **通用“重新/重做”:**
        *   "请重新帮我填写一下`[受试者编号XXX]`的`[表单类型]`。"
        *   "这份`[表单类型]`需要重做，请帮我处理。"
        *   "忽略我上传的文件，请重新生成`[受试者编号YYY]`的`[表单类型]`。"
        *   "请不要基于上传的文件，帮我重新做一份`[表单类型]`。"
        *   "帮我重填这个`[表单类型]`。"
        *   "用系统数据重新生成`[受试者编号ZZZ]`的`[表单类型]`。"

    *   **基于“最近一次”数据 (需要 Function Calling):**
        *   "请帮我找到`[受试者编号XXX]`最近一次访视的`[数据类型，如：体温/血压]`数据，并填写`[表单类型]`。"
        *   "我需要`[受试者编号YYY]`最新的`[表单类型]`，请根据系统记录生成。"
        *   "调取一下`[受试者编号ZZZ]`最近一次录入的`[数据类型]`信息，然后重新填写`[表单类型]`。"
        *   "请基于知识库里该受试者最后一次的`[数据类型]`记录，生成`[表单类型]`。"
        *   "查找`[受试者编号AAA]`最近的`[数据类型]`数据填入`[表单类型]`。"

    *   **基于“特定时间范围”数据 (需要 Function Calling):**
        *   "请帮我提取`[受试者编号XXX]`从`[开始时间，如：2024年/2024年1月1日]`开始的所有`[数据类型]`记录，并生成`[表单类型]`。"
        *   "我需要`[受试者编号YYY]`在`[特定时间点/段，如：2025年3月份/基线访视]`的`[表单类型]`，请查找数据并填写。"
        *   "请生成`[受试者编号ZZZ]`在`[开始时间]`到`[结束时间]`期间的`[表单类型]`。"
        *   "查找一下`[受试者编号AAA]` `[时间范围描述，如：2024年Q4/入组后前三次访视]`的`[数据类型]`数据，然后整理成`[表单类型]`格式。"
        *   "请帮我筛选出`[受试者编号BBB]`在`[访视A]`到`[访视B]`期间的所有`[数据类型]`记录，并填写`[表单类型]`。"
        *   "请查找并整理`[受试者编号ZZZ]`的所有`[数据类型]`记录，生成一份完整的`[表单类型]`。"

    *   **明确要求使用“结构化”数据:**
        *   "请基于系统里的结构化数据重新填写`[受试者编号XXX]`的`[表单类型]`。"
        *   "从结构化数据库里找`[数据类型]`数据，帮我重填`[表单类型]`。"

---

### Case 2.2: 纸质数据源 (Source: `Paper`)

*   **触发条件:** 属于 `Refill_Form` 意图，且用户明确提及“纸质”、“打印”、“手写”、“扫描件”、“复印件”、“线下”、“手头”、“日志本”等相关词语作为数据来源。
*   **AI 行为:** AI 可能无法直接访问物理纸张。行为可能包括：
    *   查找系统中先前被标记为“来自纸质”的已录入数据。
    *   引导用户手动输入关键信息。
    *   处理用户上传的“纸质文件”的扫描件/照片（如果具备 OCR 能力）。
    *   澄清数据来源或具体内容。
*   **CRC/CRA 常用语料:**
    *   "根据我手头这份纸质的`[文件类型，如：检查报告/访视记录]`，帮我重新填写`[表单类型]`。"
    *   "我需要录入这次访视的纸质`[数据类型]`结果，先帮我把`[表单类型]`调出来准备好。"
    *   "查找一下`[受试者编号XXX]`之前从纸质文件录入的`[数据类型]`数据，重填`[表单类型]`。"
    *   "用那份打印出来的`[文件类型]`数据，重填一下`[受试者编号YYY]`的`[表单类型]`。"
    *   "请根据手写的原始记录，重新生成`[受试者编号ZZZ]`的`[表单类型]`。"
    *   "处理一下这个扫描件里的数据，填到`[表单类型]`里。" (隐含 `Refill_Form`，因为源是纸质扫描件)

---

### Case 2.3: 病历数据源 (Source: `Medical_Record`)

*   **触发条件:** 属于 `Refill_Form` 意图，且用户明确提及“病历”、“病案”、“医疗记录”、“Source Document”、“SD”、“源文件”、“HIS”、“EMR”、“医生/护士记录”、“病程”等相关词语作为数据来源。
*   **AI 行为:** **（根据当前设定）返回不支持此操作的提示。** 例如：“抱歉，我目前无法直接从病历/源文件中提取信息来重新填写表单。请您提供结构化数据或手动输入。”
*   **CRC/CRA 常用语料 (预期会触发“不支持”回应):**
    *   "根据病历里面的`[数据类型，如：生命体征/诊断信息]`数据，生成`[表单类型]`。"
    *   "请从`[受试者编号XXX]`的电子病历里提取`[数据类型]`数据，重填`[表单类型]`。"
    *   "基于`[受试者编号YYY]`的 Source Document，重新填写`[表单类型]`。"
    *   "帮我看看源文件，然后重新填写这份`[表单类型]`。"
    *   "调取 HIS 系统中关于`[数据类型]`的部分，填到`[表单类型]`里。"
    *   "参考病程记录，重填`[表单类型]`。"

---

## 数据源近义词/相关说法 (用于意图识别和槽位填充)

### 纸质 (Paper) 相关

*   **直接:** 纸质版, 纸质文件, 纸张, 打印件, 打印出来, 手写记录, 扫描件, 复印件
*   **间接/场景:** 原始记录 (有时指纸质), 线下文件, 手头的材料, 访视日志本 (纸质), 化验单/报告 (若语境暗示物理件)

### 病历 (Medical_Record) 相关

*   **直接:** 病历, 病案, 医疗记录, 就诊记录, 患者记录, 住院病历, 门诊病历, 电子病历 (EMR/EHR), HIS 系统记录
*   **临床研究特定:** 原始病历, 源病历, Source Document (SD), 源文件
*   **内容相关:** 医生记录, 医嘱, 护士记录, 病程记录, 出院小结, 入院记录

---

## 关键考虑因素

*   **占位符:** `[占位符]` 代表实际使用中会变化的具体信息（如受试者编号、表单类型、数据类型、时间等）。
*   **上下文依赖:** AI 需要理解对话上下文，特别是区分 `Fill_Form` 和 `Refill_Form`。
*   **歧义处理:** 对于模糊指令（如仅说“填表”或使用有歧义的词语），AI 应主动澄清。
*   **适应性:** 此框架和语料可替换关键词，适应不同类型的表单填写任务。
*   **Function Calling:** `Refill_Form` 的许多场景（特别是基于时间、最新数据）需要后端能力（Function Calling）来查询数据库或知识库。
 