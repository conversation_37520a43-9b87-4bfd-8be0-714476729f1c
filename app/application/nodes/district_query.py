"""
行政区划查询节点 - 负责查询行政区划信息
"""
import logging
from typing import Dict, Any

from langchain_core.tools import BaseTool
from app.domain.schemas import AssistantState, AmapDistrictResponse, BaseResponse

# 配置日志
logger = logging.getLogger(__name__)

def query_district(state: AssistantState, tools: list[BaseTool]) -> Dict[str, Any]:
    """
    查询行政区划信息
    
    Args:
        state: 当前助手状态
        tools: LangChain 工具列表
        
    Returns:
        包含更新状态的字典
    """
    # 检查位置是否存在且有效
    # 注意：如果 location 是空字符串，也应该认为无效
    if state.location is None or state.location.strip() == "":
        error_msg = "未能识别出有效的位置信息"
        logger.warning(error_msg)
        return {"error_message": error_msg, "district_result": {"code": 400, "message": error_msg, "data": None}}

    # Now it's safe to access 'location'
    location = state.location

    # 查找对应的工具
    district_tool = next((tool for tool in tools if tool.name == "query_amap_district"), None)
    if not district_tool:
        error_msg = "行政区划查询工具不可用"
        logger.error(error_msg)
        return {"error_message": error_msg, "district_result": {"code": 500, "message": error_msg, "data": None}}

    # 执行工具调用
    updates: Dict[str, Any] = {}
    try:
        result = district_tool.invoke({"keywords": location})

        # 添加详细的调试日志
        logger.info(f"API 响应类型: {type(result)}")

        # 将响应转换为字典格式
        if hasattr(result, "model_dump"):
            # Pydantic v2
            result_dict = result.model_dump()
            logger.debug(f"使用 model_dump() 转换响应: {result_dict}")
        elif hasattr(result, "dict"):
            # Pydantic v1
            result_dict = result.dict()
            logger.debug(f"使用 dict() 转换响应: {result_dict}")
        else:
            # 如果不是 Pydantic 模型，尝试直接使用
            if isinstance(result, dict):
                result_dict = result
                logger.info(f"API 响应内容: {result_dict}")
            else:
                # 非字典且非 Pydantic 模型的响应视为错误
                logger.error(f"行政区划查询返回了无法处理的响应类型: {type(result)}")
                updates['error_message'] = "行政区划查询返回了无法处理的响应"
                return updates

        # 处理成功响应 - 转换并标准化
        if result_dict.get('status') == '1':
            # API 调用成功
            districts_data = result_dict.get('districts')
            if districts_data and isinstance(districts_data, list) and districts_data:
                # 获取 adcode (现在可以在这里安全获取了)
                try:
                    first_district = districts_data[0]
                    if isinstance(first_district, dict) and 'adcode' in first_district:
                        adcode = first_district['adcode']
                        updates['adcode'] = adcode
                        logger.info(f"获取到adcode: {adcode}")
                    else:
                        logger.warning("第一个区划信息格式不正确或缺少 adcode")
                except (IndexError, TypeError) as e:
                    logger.warning(f"解析 adcode 时出错: {e}")

                # 创建嵌套数据
                nested_data = {'districts': districts_data}
                standardized_result = {
                    'code': 0,
                    'message': 'OK',
                    'data': nested_data
                }
                logger.debug(f"标准化区划响应成功: {standardized_result}")
            else:
                # API 成功，但缺少 districts 数据
                logger.warning(f"API 成功但缺少有效的 districts 数据: {result_dict}")
                standardized_result = {
                    'code': 0,
                    'message': 'OK - No district data available',
                    'data': None
                }
                updates['error_message'] = f"未能找到与 '{location}' 相关的行政区划信息。"
        else:
            # API 调用失败
            error_code = int(result_dict.get('infocode', 99999))
            error_message = result_dict.get('info', 'Unknown API Error')
            logger.warning(f"区划查询 API 失败 ({error_code}): {error_message}")
            standardized_result = {
                'code': error_code,
                'message': error_message,
                'data': None
            }
            updates['error_message'] = f"区划 API 错误 ({error_code}): {error_message}"

        # 存储标准化后的响应数据
        updates['district_result'] = standardized_result

    except Exception as e:
        error_msg = f"查询行政区划时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        # Overwrite updates with error info in case of exception
        updates = {
            "error_message": error_msg,
            "district_result": {"code": 500, "message": error_msg, "data": None}
        }
        
    return updates
