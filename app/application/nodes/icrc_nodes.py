import copy
import importlib
import logging
import os
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Callable
from functools import wraps

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

from app.domain.schemas import AssistantState, DataSource, CrfTable
from app.function_call import structure, paper
from app.models.schemas import QueryRequest, KnowledgeSlice
from app.services.query_service import QueryService
from app.utils.direct_llm import call_llm_api  # 导入统一的 API 调用工具
from app.utils.changsanyuan.run_get_modify_evidence import get_modify_evidence  # 导入溯源方法
from app.utils.visit_formatter import format_visits_data  # 导入visits数据格式化工具
from app.utils.duplicate_key_json_parser import DuplicateKeyJsonParser  # 导入自定义解析器
from app.utils.prime.get_extract import get_extract_ents_v2  # 导入时间实体提取工具

logger = logging.getLogger(__name__)

query_service = QueryService()


# 添加trace和耗时统计的装饰器
def trace_time(func_name: str = None):
    """
    装饰器：记录函数执行时间和添加trace日志

    Args:
        func_name: 函数名称，如果为None则使用被装饰函数的名称
    """

    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数名
            name = func_name or func.__name__
            # 记录开始时间
            start_time = time.time()
            # 记录开始trace
            logger.info(f"TRACE - 开始执行: {name}")

            try:
                # 执行原函数
                result = func(*args, **kwargs)
                # 计算耗时
                elapsed_time = time.time() - start_time
                # 记录结束trace和耗时
                logger.info(f"TRACE - 结束执行: {name} - 耗时: {elapsed_time:.4f}秒")
                return result
            except Exception as e:
                # 计算耗时
                elapsed_time = time.time() - start_time
                # 记录异常trace和耗时
                logger.error(f"TRACE - 执行异常: {name} - 耗时: {elapsed_time:.4f}秒 - 错误: {str(e)}")
                raise

        return wrapper

    return decorator


# --- 校验函数 ---
@trace_time("校验app_params")
def validate_app_params(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验app_params是否存在"""
    app_params = state.app_params
    if not app_params:
        logger.warning("ICRC Extractor: 'app_params' missing in state.")
        return {"icrc_form_data": None, "final_response": "Request state is missing required parameters."}
    return None


@trace_time("校验metaInfo")
def validate_meta_info(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验metaInfo是否存在"""
    meta_info = state.app_params.metaInfo
    if not meta_info:
        logger.warning("ICRC Extractor: No 'metaInfo' found in app_params.")
        return {"icrc_form_data": None, "final_response": "Request is missing metadata."}
    return None


@trace_time("校验crfInfo")
def validate_crf_info(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验crfInfo和tableList是否存在"""
    crf_info = state.app_params.metaInfo.crfInfo
    if not crf_info or not state.app_params.metaInfo.crfInfo.tableList:
        logger.warning("ICRC Extractor: No 'crfInfo' or 'tableList' found.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "No form definition found to fill."}
    return None


@trace_time("校验visits")
def validate_visits(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验visits是否存在"""
    visits = state.app_params.metaInfo.visits
    if not visits:
        logger.warning("ICRC Extractor: No 'visits' data found for extraction.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "找不到对应的访视."}
    return None


@trace_time("校验tableList")
def validate_table_list(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验tableList是否为空"""
    if not state.app_params.metaInfo.crfInfo.tableList:
        logger.warning("ICRC Extractor: 'tableList' is empty.")
        return {"icrc_form_data": [], "final_response": "Form definition list is empty."}
    return None


@trace_time("校验crfIndexList")
def validate_crf_index_list(state: AssistantState, target_table) -> Optional[Dict[str, Any]]:
    """校验crfIndexList是否存在"""
    target_indices = target_table.crfIndexList
    if not target_indices:
        logger.warning(f"ICRC Extractor: No 'crfIndexList' in first table ('{target_table.crfTableName}').")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "First form definition is missing fields to fill."}
    return None


@trace_time("校验target_field_names")
def validate_target_field_names(target_indices) -> Tuple[Optional[Dict[str, Any]], List[str]]:
    """校验target_field_names是否有效，并返回有效的字段名列表"""
    target_field_names = [index.crfIndexName for index in target_indices if hasattr(index, 'crfIndexName')]
    if not target_field_names:
        logger.warning("ICRC Extractor: No valid crfIndexName found in target_indices.")
        return {"icrc_form_data": None, "final_response": "No target field names defined in the form."}, []
    return None, target_field_names


@trace_time("校验visit数据")
def validate_visit_data(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验visit数据是否有效"""
    if not any(visit.data for visit in state.app_params.metaInfo.visits):
        logger.warning("ICRC Extractor: No valid data found in visits.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "No valid data found in visit records to extract from."}
    return None


@trace_time("生成目标字段schema")
def generate_target_fields_schema(target_indices) -> Tuple[Dict[str, str], str]:
    """
    根据目标索引生成字段schema及其格式化字符串

    Args:
        target_indices: CRF索引列表

    Returns:
        Tuple[Dict[str, str], str]: 包含字段schema字典和格式化后的字符串
    """
    # 生成目标字段schema
    target_fields_schema = {}
    for index_item in target_indices:
        if hasattr(index_item, 'crfIndexName') and hasattr(index_item, 'crfIndexType'):
            # 添加主字段
            target_fields_schema[index_item.crfIndexName] = index_item.crfIndexType
            # 添加对应的hit_value字段
            target_fields_schema[f"{index_item.crfIndexName}_hit_value"] = "string"

    # 将schema转换为格式化的字符串，避免JSON格式与LangChain模板变量语法冲突
    target_fields_schema_str = ""
    for field_name, field_type in target_fields_schema.items():
        target_fields_schema_str += f'"{field_name}": "{field_type}"\n'

    return target_fields_schema, target_fields_schema_str


@trace_time("处理记录内容")
def process_record_contents(records) -> Tuple[Dict[str, str], List[str], Dict[str, str]]:
    """
    处理记录内容，提取有效内容并映射记录ID

    Args:
        records: 记录列表

    Returns:
        Tuple[Dict[str, str], List[str], Dict[str, str]]:
            包含记录内容映射、合并内容部分和全局记录内容映射
    """
    record_contents_map = {}
    combined_content_parts = []
    global_record_content_map = {}

    for record in records:
        content = record.content
        record_id = record.recordId
        global_record_content_map[record_id] = content
        if content and isinstance(content, str) and content.strip():
            clean_content = content.strip()
            record_contents_map[record_id] = clean_content
            combined_content_parts.append(clean_content)

    return record_contents_map, combined_content_parts, global_record_content_map


@trace_time("处理visit数据内容")
def process_visit_data_contents(visit_data) -> Tuple[Dict[str, str], List[str], Dict[str, str]]:
    """
    处理整个visit.data的内容，将所有data_item中的records合并处理

    Args:
        visit_data: visit.data列表，包含多个data_item

    Returns:
        Tuple[Dict[str, str], List[str], Dict[str, str]]:
            包含记录内容映射、合并内容部分和全局记录内容映射
    """
    record_contents_map = {}
    combined_content_parts = []
    global_record_content_map = {}

    for data_item in visit_data:
        # 获取data_item的元信息，用于内容分隔
        table_name_desc = getattr(data_item, 'tableNameDesc', '')
        table_name = getattr(data_item, 'tableName', '')

        # 为当前data_item添加标识性分隔符（可选）
        data_item_header = ""
        if table_name_desc:
            data_item_header = f"【{table_name_desc}】"
        elif table_name:
            data_item_header = f"【{table_name}】"

        # 处理当前data_item中的所有records
        records = data_item.records
        data_item_contents = []

        for record in records:
            content = record.content
            record_id = record.recordId
            global_record_content_map[record_id] = content

            if content and isinstance(content, str) and content.strip():
                clean_content = content.strip()
                record_contents_map[record_id] = clean_content
                data_item_contents.append(clean_content)

        # 将当前data_item的内容合并
        if data_item_contents:
            if data_item_header:
                # 如果有标题，将标题和内容一起添加
                data_item_combined = f"{data_item_header}\n" + "\n".join(data_item_contents)
            else:
                data_item_combined = "\n".join(data_item_contents)

            combined_content_parts.append(data_item_combined)

    return record_contents_map, combined_content_parts, global_record_content_map


@trace_time("查找字段值和hit_value")
def find_field_value_and_hit_value(result_item: Dict[str, Any], field_name: str, used_keys: set = None) -> Tuple[
    Any, str, str]:
    """
    在result_item中查找字段值和对应的hit_value，处理重复键重命名的情况

    Args:
        result_item: LLM解析后的结果项
        field_name: 原始字段名
        used_keys: 已使用的键名集合，用于避免重复使用同一个键

    Returns:
        Tuple[Any, str, str]: (字段值, 实际使用的键名, hit_value键名)
    """
    if used_keys is None:
        used_keys = set()

    # 首先尝试原始字段名（如果还没有被使用）
    if field_name in result_item and field_name not in used_keys:
        hit_value_key = f"{field_name}_hit_value"
        used_keys.add(field_name)
        return result_item[field_name], field_name, hit_value_key

    # 如果原始字段名不存在或已被使用，查找所有重命名后的键
    # 重命名规则是 field_name_2, field_name_3, ...
    # 使用列表推导式找到所有匹配的重命名键
    renamed_keys = [key for key in result_item.keys()
                    if key.startswith(f"{field_name}_") and key != f"{field_name}_hit_value" and key not in used_keys]

    if renamed_keys:
        # 按照数字后缀排序，取最小的那个（通常是_2）
        def extract_suffix_number(key):
            try:
                suffix = key.replace(f"{field_name}_", "")
                return int(suffix)
            except ValueError:
                return float('inf')  # 如果不是数字后缀，排到最后

        # 排序并取第一个
        renamed_keys.sort(key=extract_suffix_number)
        selected_key = renamed_keys[0]
        suffix = selected_key.replace(f"{field_name}_", "")
        hit_value_key = f"{field_name}_hit_value_{suffix}"

        used_keys.add(selected_key)
        logger.info(
            f"找到重命名字段: '{field_name}' -> '{selected_key}' (从{len(renamed_keys)}个候选中选择，已排除{len([k for k in result_item.keys() if k.startswith(f'{field_name}_') and k != f'{field_name}_hit_value']) - len(renamed_keys)}个已使用的键)")
        return result_item[selected_key], selected_key, hit_value_key

    # 如果都没有找到，返回None
    return None, field_name, f"{field_name}_hit_value"


@trace_time("调用LLM进行信息提取")
def call_llm_for_extraction(prompt_text: str, is_repeat: int, state: AssistantState) -> Tuple[
    List[Dict[str, Any]], bool]:
    """
    调用LLM进行信息提取

    Args:
        prompt_text: 提示词文本
        is_repeat: 是否为循环表单
        state: 当前状态

    Returns:
        Tuple[List[Dict[str, Any]], bool]: 提取结果列表和是否成功标志
    """
    try:
        # 记录开始时间
        start_time = time.time()

        # 调用LLM API
        provider = os.getenv("LLM_PROVIDER", "deepseek")  # 从环境变量获取 provider，默认为 deepseek
        logger.info(f"ICRC Extractor: Calling {provider} API directly")
        stream = False
        if state.app_params.chatType == "stream":
            stream = True
            logger.info("Stream mode enabled.")

        # 调用API
        logger.info(f"TRACE - 开始调用LLM API")
        api_start_time = time.time()
        content, thinking_process = call_llm_api(
            prompt=prompt_text,
            provider=provider,
            model=os.getenv("LLM_MODEL_NAME"),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0")),
            is_form_extraction=True  # 标记为表单信息提取阶段，不发送answering事件
        )
        api_elapsed_time = time.time() - api_start_time
        logger.info(f"TRACE - 结束调用LLM API - 耗时: {api_elapsed_time:.4f}秒")

        # 处理思考过程
        logger.info(f"TRACE - 开始处理思考过程")
        thinking_start_time = time.time()
        if thinking_process and isinstance(thinking_process, str):
            logger.info(f"ICRC Extractor: Found thinking process from direct API call.")
            if not hasattr(state, 'thought_log') or state.thought_log is None:
                state.thought_log = []
            state.thought_log.append(thinking_process)
            logger.info("ICRC Extractor: Added thinking process to state.thought_log.")
        else:
            logger.warning("ICRC Extractor: No thinking process found in API response.")
        thinking_elapsed_time = time.time() - thinking_start_time
        logger.info(f"TRACE - 结束处理思考过程 - 耗时: {thinking_elapsed_time:.4f}秒")

        # 处理LLM内容输出
        logger.info(f"TRACE - 开始处理LLM内容输出")
        content_start_time = time.time()
        if content and isinstance(content, str):
            logger.info(f"ICRC Extractor: Found LLM content from direct API call.")
            if not hasattr(state, 'llm_content') or state.llm_content is None:
                state.llm_content = []
            state.llm_content.append(content)
            logger.info("ICRC Extractor: Added LLM content to state.llm_content.")
        else:
            logger.warning("ICRC Extractor: No LLM content found in API response.")
        content_elapsed_time = time.time() - content_start_time
        logger.info(f"TRACE - 结束处理LLM内容输出 - 耗时: {content_elapsed_time:.4f}秒")

        # 设置输出解析器 - 使用自定义的重复键解析器
        logger.info(f"TRACE - 开始设置输出解析器")
        parser_start_time = time.time()
        if is_repeat == 1:
            output_parser = DuplicateKeyJsonParser(pydantic_object=List[Dict[str, Any]])
            logger.info("ICRC Extractor: Using DuplicateKeyJsonParser for JSON list (isRepeat=1)")
        else:
            output_parser = DuplicateKeyJsonParser(pydantic_object=Dict[str, Any])
            logger.info("ICRC Extractor: Using DuplicateKeyJsonParser for JSON object (isRepeat=0)")
        parser_elapsed_time = time.time() - parser_start_time
        logger.info(f"TRACE - 结束设置输出解析器 - 耗时: {parser_elapsed_time:.4f}秒")

        # 解析内容
        logger.info(f"TRACE - 开始解析LLM输出内容")
        parse_start_time = time.time()
        if content and isinstance(content, str):
            llm_output = output_parser.parse(content)
            logger.info(f"ICRC Extractor: Parsed LLM Output type: {type(llm_output)}")
        else:
            logger.error("ICRC Extractor: API response missing content or content is not a string")
            llm_output = [] if is_repeat == 1 else {}
        parse_elapsed_time = time.time() - parse_start_time
        logger.info(f"TRACE - 结束解析LLM输出内容 - 耗时: {parse_elapsed_time:.4f}秒")

        # 确保输出始终是列表
        logger.info(f"TRACE - 开始处理输出格式")
        format_start_time = time.time()
        if isinstance(llm_output, dict):
            extracted_results = [llm_output]
        elif isinstance(llm_output, list):
            extracted_results = llm_output
        else:
            logger.warning(
                f"ICRC Extractor: Unexpected LLM output type: {type(llm_output)}. Skipping results.")
            extracted_results = []
        format_elapsed_time = time.time() - format_start_time
        logger.info(f"TRACE - 结束处理输出格式 - 耗时: {format_elapsed_time:.4f}秒")

        # 计算总耗时
        total_elapsed_time = time.time() - start_time
        logger.info(f"TRACE - LLM调用及处理总耗时: {total_elapsed_time:.4f}秒")

        return extracted_results, True

    except Exception as e:
        logger.error(f"ICRC Extractor: LLM chain invocation failed. Error: {e}")
        return [], False


@trace_time("处理提取结果")
def process_extraction_results(extracted_results: List[Dict[str, Any]], record_contents_map: Dict[str, str],
                               visit_id: str) -> List[Dict[str, Any]]:
    """
    处理提取结果并映射记录ID

    Args:
        extracted_results: 提取结果列表
        record_contents_map: 记录内容映射
        visit_id: 访问ID

    Returns:
        List[Dict[str, Any]]: 处理后的结果列表
    """
    processed_results = []

    for result_item in extracted_results:
        found_record_id = None
        hit_value_field = None
        hit_value = None

        # 查找第一个匹配的hit_value
        logger.info(f"TRACE - 开始查找匹配的hit_value")
        hit_value_start_time = time.time()
        for key, value in result_item.items():
            if key.endswith("_hit_value") and isinstance(value, str) and value.strip():
                hit_value_field = key
                hit_value_content = value.strip()

                # 在原始记录内容中搜索hit_value
                logger.info(f"记录ID映射 - 开始查找hitValue: '{hit_value_content}'")
                logger.info(f"记录ID映射 - 可用记录数量: {len(record_contents_map)}")
                for rec_id, rec_content in record_contents_map.items():
                    logger.info(f"记录ID映射 - 检查记录: {rec_id}, 内容长度: {len(rec_content)}, 内容: '{rec_content[:100]}...'")
                    if hit_value_content in rec_content:
                        found_record_id = rec_id
                        logger.info(f"记录ID映射 - 匹配成功: hitValue='{hit_value_content}' 映射到 record_id={found_record_id}")
                        logger.info(f"记录ID映射 - 匹配位置: {rec_content.find(hit_value_content)}")
                        break
                    else:
                        logger.info(f"记录ID映射 - 匹配失败: hitValue在记录{rec_id}中未找到")

                if found_record_id:
                    hit_value = hit_value_content
                    break
        hit_value_elapsed_time = time.time() - hit_value_start_time
        logger.info(f"TRACE - 结束查找匹配的hit_value - 耗时: {hit_value_elapsed_time:.4f}秒")

        # 如果找不到记录ID，使用回退策略
        if not found_record_id:
            logger.info(f"TRACE - 开始使用回退策略查找记录ID")
            fallback_start_time = time.time()
            if record_contents_map:
                found_record_id = next(iter(record_contents_map))
                logger.warning(
                    f"ICRC Extractor: Could not map result item using any _hit_value. Associating with first record_id: {found_record_id}")
            else:
                logger.warning("ICRC Extractor: Could not map result item and no record_ids available.")
                continue
            fallback_elapsed_time = time.time() - fallback_start_time
            logger.info(f"TRACE - 结束使用回退策略查找记录ID - 耗时: {fallback_elapsed_time:.4f}秒")

        # 添加记录ID和访问ID到结果项
        result_item['__found_record_id__'] = found_record_id
        result_item['__visit_id__'] = visit_id
        if hit_value_field:
            result_item[hit_value_field] = hit_value
        processed_results.append(result_item)

    return processed_results


@trace_time("处理单个visit")
def process_single_visit(visit, target_field_names, target_fields_str, target_fields_schema_str,
                         knowledge_context, prompt_template, is_repeat, state):
    """
    处理单个visit的数据提取

    Args:
        visit: 单个visit对象
        target_field_names: 目标字段名列表
        target_fields_str: 目标字段字符串
        target_fields_schema_str: 目标字段schema字符串
        knowledge_context: 知识上下文
        prompt_template: 提示词模板
        is_repeat: 是否为循环表单
        state: 当前状态

    Returns:
        Dict: 包含visit处理结果的字典
    """
    visit_id = visit.visitId
    thread_id = threading.current_thread().ident
    logger.info(f"TRACE - 线程{thread_id}开始处理visit: {visit_id}")
    single_visit_start_time = time.time()

    visit_data = visit.data
    current_visit_results = []
    visit_processed = False
    found_complete_form = False
    record_content_updates = {}

    # 新的处理方式：将整个visit.data合并处理
    logger.info(f"TRACE - 线程{thread_id}使用visit.data级别合并处理")

    # 使用新的函数处理整个visit.data
    record_contents_map, combined_content_parts, record_content_batch = process_visit_data_contents(visit_data)
    # 更新记录内容映射
    record_content_updates.update(record_contents_map)

    # Skip if no valid content in this visit
    if not combined_content_parts:
        logger.info(f"TRACE - 线程{thread_id}跳过无有效内容的visit")
    else:
        # Combine content for LLM input
        source_text = "\n\n".join(combined_content_parts)

        logger.info(
            f"TRACE - 线程{thread_id}处理visit {visit_id}，合并了{len(record_contents_map)}个记录的内容（visit.data级别）")

        # --- Prepare and Invoke LLM for the combined content ---
        logger.info(f"TRACE - 线程{thread_id}开始准备LLM输入")
        llm_prep_start_time = time.time()

        # Prepare the input dictionary for the prompt template
        if not knowledge_context:
            current_knowledge_context = "未提供"
        else:
            current_knowledge_context = knowledge_context

        # 预处理时间实体
        current_knowledge_context = preprocess_time_entities(current_knowledge_context, source_text)

        prompt_input = {
            "target_fields_list_placeholder": target_fields_str,
            "source_text": source_text,
            "target_fields_schema": target_fields_schema_str,
            "knowledge_context": current_knowledge_context
        }

        llm_prep_elapsed_time = time.time() - llm_prep_start_time
        logger.info(f"TRACE - 线程{thread_id}结束准备LLM输入 - 耗时: {llm_prep_elapsed_time:.4f}秒")

        # --- Direct API Invocation ---
        logger.info(f"TRACE - 线程{thread_id}开始生成提示词")
        prompt_gen_start_time = time.time()
        # First generate the prompt
        prompt_value = prompt_template.invoke(prompt_input)
        prompt_gen_elapsed_time = time.time() - prompt_gen_start_time
        logger.info(f"TRACE - 线程{thread_id}结束生成提示词 - 耗时: {prompt_gen_elapsed_time:.4f}秒")

        try:
            # 将 PromptValue 对象转换为文本
            logger.info(f"TRACE - 线程{thread_id}开始转换提示词为文本")
            prompt_convert_start_time = time.time()
            prompt_text = prompt_value.to_string()
            logger.info(f"线程{thread_id}渲染的提示词文本: {prompt_text[:100]}...")
            prompt_convert_elapsed_time = time.time() - prompt_convert_start_time
            logger.info(f"TRACE - 线程{thread_id}结束转换提示词为文本 - 耗时: {prompt_convert_elapsed_time:.4f}秒")

            # 调用LLM进行提取
            extracted_results, success = call_llm_for_extraction(prompt_text, is_repeat, state)

            if not success:
                logger.warning(f"TRACE - 线程{thread_id}LLM提取失败，visit: {visit_id}")
                extracted_results = []

            # --- 处理提取结果并映射记录ID ---
            processed_results = process_extraction_results(extracted_results, record_contents_map, visit_id)
            current_visit_results.extend(processed_results)

            # Check for completeness if non-repeat form (is_repeat=0)
            logger.info(f"TRACE - 线程{thread_id}开始检查表单完整性")
            check_complete_start_time = time.time()
            if is_repeat == 0 and extracted_results:  # Check if we got any result
                # Assuming the first result is the one to check for completeness
                first_result = extracted_results[0]
                all_fields_present = all(field in first_result for field in target_field_names)
                if all_fields_present:
                    found_complete_form = True
                    logger.info(f"TRACE - 线程{thread_id}找到完整的非循环表单数据，visit: {visit_id}")
            check_complete_elapsed_time = time.time() - check_complete_start_time
            logger.info(f"TRACE - 线程{thread_id}结束检查表单完整性 - 耗时: {check_complete_elapsed_time:.4f}秒")

            visit_processed = True  # Mark visit as processed if LLM call succeeded

        except Exception as e:
            logger.error(f"TRACE - 线程{thread_id}LLM调用失败，visit {visit_id}. 错误: {e}")
            # Optionally add partial/error state to results
            current_visit_results.append({"error": str(e), "visit_id": visit_id})

    single_visit_elapsed_time = time.time() - single_visit_start_time
    logger.info(f"TRACE - 线程{thread_id}结束处理visit: {visit_id} - 耗时: {single_visit_elapsed_time:.4f}秒")

    return {
        "visit_id": visit_id,
        "thread_id": thread_id,
        "results": current_visit_results,
        "processed": visit_processed,
        "found_complete_form": found_complete_form,
        "record_content_updates": record_content_updates,
        "processing_time": single_visit_elapsed_time
    }


# --- ICRC 表单信息提取 ---
@trace_time("ICRC表单信息提取")
def icrc_extract_form_data(state: AssistantState) -> Dict[str, Any]:  # llm is now required
    """
    Extracts data from state.app_params.metaInfo.visits text content
    to fill the form defined in state.app_params.metaInfo.crfInfo.
    Uses LLM for structured extraction based on crfIndexNames.

    如果意图是Refill_Form，则调用handle_refill_form函数处理。
    """
    logger.info("--- Executing ICRC Form Data Extractor Node (LLM-Based) ---")
    # 打印 state.icrc_extracted_details
    if state.icrc_extracted_details:
        logger.info(f"ICRC Extractor: Extracted details: {state.icrc_extracted_details}")

    # 检查意图是否为Refill_Form
    if hasattr(state, 'icrc_intent') and state.icrc_intent == "Refill_Form":
        logger.info("TRACE - 开始处理Refill_Form意图")
        refill_start_time = time.time()
        logger.info("ICRC Extractor: 检测到Refill_Form意图，调用handle_refill_form处理")
        # 调用handle_refill_form获取表单数据
        refill_result = handle_refill_form(state)

        # 检查是否成功获取到数据
        if refill_result.get("crf_result"):
            logger.info("ICRC Extractor: Refill_Form处理成功获取数据，将数据设置到state中并继续处理")

            # 如果有visits数据，也更新到state中
            if refill_result.get("visits"):
                # 使用工具函数格式化visits数据
                visits_data = refill_result.get("visits")
                try:
                    # 调用工具函数格式化visits数据
                    logger.info("TRACE - 开始格式化visits数据")
                    format_start_time = time.time()
                    visit_objects = format_visits_data(visits_data)
                    format_elapsed_time = time.time() - format_start_time
                    logger.info(f"TRACE - 结束格式化visits数据 - 耗时: {format_elapsed_time:.4f}秒")

                    if visit_objects:
                        # 更新state中的visits数据
                        state.app_params.metaInfo.visits = visit_objects
                        logger.info(
                            f"ICRC Extractor: 已将Refill_Form获取的{len(visit_objects)}个visits数据设置到state中")
                    else:
                        logger.warning("格式化visits数据失败，保留原始visits数据")
                except Exception as e:
                    logger.error(f"处理visits数据时出错: {str(e)}")
                    # 如果处理失败，保留原始visits数据
                    logger.warning("处理visits数据出错，保留原始visits数据")
            else:
                logger.warning("ICRC Extractor: Refill_Form处理未获取到有效的表单数据，返回原始结果")
        else:
            state.app_params.metaInfo.visits = []

        refill_elapsed_time = time.time() - refill_start_time
        logger.info(f"TRACE - 结束处理Refill_Form意图 - 耗时: {refill_elapsed_time:.4f}秒")

    # --- 1. 校验输入数据 ---
    logger.info("TRACE - 开始校验输入数据")
    validation_start_time = time.time()

    validation_result = validate_app_params(state)
    if validation_result:
        return validation_result

    validation_result = validate_meta_info(state)
    if validation_result:
        return validation_result

    validation_result = validate_crf_info(state)
    if validation_result:
        return validation_result

    validation_result = validate_visits(state)
    if validation_result:
        return validation_result

    validation_result = validate_table_list(state)
    if validation_result:
        return validation_result

    validation_elapsed_time = time.time() - validation_start_time
    logger.info(f"TRACE - 结束校验输入数据 - 耗时: {validation_elapsed_time:.4f}秒")

    # --- 2. 获取目标字段和准备上下文 ---
    logger.info("TRACE - 开始获取目标字段和准备上下文")
    prepare_start_time = time.time()

    crf_info = state.app_params.metaInfo.crfInfo
    visits = state.app_params.metaInfo.visits
    knowledge = state.app_params.metaInfo.knowledge

    target_table = state.app_params.metaInfo.crfInfo.tableList[0]  # Process first table

    validation_result = validate_crf_index_list(state, target_table)
    if validation_result:
        return validation_result

    target_indices = target_table.crfIndexList
    validation_result, target_field_names = validate_target_field_names(target_indices)
    if validation_result:
        return validation_result

    logger.info(f"ICRC Extractor: Target fields for LLM extraction: {target_field_names}")

    validation_result = validate_visit_data(state)
    if validation_result:
        return validation_result

    prepare_elapsed_time = time.time() - prepare_start_time
    logger.info(f"TRACE - 结束获取目标字段和准备上下文 - 耗时: {prepare_elapsed_time:.4f}秒")

    # --- 3. Define LLM Prompt for Extraction ---
    logger.info("TRACE - 开始准备LLM提示词")
    prompt_start_time = time.time()

    # Create a string list of target fields for the prompt
    target_fields_str = "\n".join([f"- {name}" for name in target_field_names])

    # 生成目标字段schema并转换为格式化字符串
    target_fields_schema, target_fields_schema_str = generate_target_fields_schema(target_indices)

    logger.info(f"ICRC Extractor: Generated target fields schema: {target_fields_schema}")

    # Load the base template from the external .md file
    try:
        logger.info("TRACE - 开始加载提示词模板")
        template_start_time = time.time()
        prompt_file_path = Path(__file__).parent / "icrc_extract_form_data.md"
        base_prompt_template = prompt_file_path.read_text(encoding='utf-8')
        template_elapsed_time = time.time() - template_start_time
        logger.info(f"TRACE - 结束加载提示词模板 - 耗时: {template_elapsed_time:.4f}秒")
    except FileNotFoundError:
        logger.error("ICRC Extractor: Prompt template file 'icrc_extract_form_data.md' not found.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "Internal error: Prompt template file missing."}
    except Exception as e:
        logger.error(f"ICRC Extractor: Error reading prompt template file 'icrc_extract_form_data.md': {e}")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "Internal error: Could not read prompt template."}

    # --- 3.5 检索知识库增强提示词 ---
    logger.info("TRACE - 开始检索知识库增强提示词")
    knowledge_start_time = time.time()

    # 检查用户上传的知识
    if state.user_uploaded_knowledge:
        logger.info(f"ICRC Extractor: 检测到{len(state.user_uploaded_knowledge)}条用户上传的知识")
        for i, k_file in enumerate(state.user_uploaded_knowledge):
            logger.info(f"用户知识 {i + 1}: fileId={k_file.fileId}, fileName={k_file.fileName}")
            if k_file.fileId in state.user_uploaded_knowledge_content:
                content_preview = state.user_uploaded_knowledge_content[k_file.fileId][:50]
                logger.info(f"内容预览: {content_preview}...")
            else:
                logger.warning(f"未找到知识内容: fileId={k_file.fileId}")
    else:
        logger.info("ICRC Extractor: 未检测到用户上传的知识")

    # 为每个字段单独检索知识库，同时传入state以处理用户上传的知识
    knowledge_context, knowledge_slices = recallKnowledge(knowledge, target_field_names, state)

    # 将知识切片添加到state中
    state.knowledge_slice = knowledge_slices
    logger.info(f"ICRC Extractor: Added {len(knowledge_slices)} knowledge slices to state")

    knowledge_elapsed_time = time.time() - knowledge_start_time
    logger.info(f"TRACE - 结束检索知识库增强提示词 - 耗时: {knowledge_elapsed_time:.4f}秒")

    # Define the prompt template ONCE using the processed template string
    logger.info("TRACE - 开始创建提示词模板")
    template_create_start_time = time.time()
    prompt_template = PromptTemplate(
        template=base_prompt_template,
        input_variables=["knowledge_context", "target_fields_list_placeholder", "target_fields_schema", "source_text"],
        template_format="jinja2"
    )
    template_create_elapsed_time = time.time() - template_create_start_time
    logger.info(f"TRACE - 结束创建提示词模板 - 耗时: {template_create_elapsed_time:.4f}秒")

    prompt_elapsed_time = time.time() - prompt_start_time
    logger.info(f"TRACE - 结束准备LLM提示词 - 耗时: {prompt_elapsed_time:.4f}秒")

    # --- 4. 为每个visit独立处理并提取数据 ---
    logger.info("TRACE - 开始处理visit数据")
    visit_process_start_time = time.time()

    # 获取isRepeat参数，判断是否为循环表单
    is_repeat = state.app_params.metaInfo.isRepeat
    logger.info(f"ICRC Extractor: Form isRepeat = {is_repeat}")

    # 检查是否启用多线程处理
    enable_concurrent = os.getenv("ENABLE_CONCURRENT_VISIT_PROCESSING", "true").lower() == "true"
    logger.info(f"ICRC Extractor: Concurrent processing enabled = {enable_concurrent}")

    if enable_concurrent:
        # 使用并发处理visits
        visit_results, visit_count, successful_visits, recordContentMap = process_visits_concurrently(
            visits=state.app_params.metaInfo.visits,
            target_field_names=target_field_names,
            target_fields_str=target_fields_str,
            target_fields_schema_str=target_fields_schema_str,
            knowledge_context=knowledge_context,
            prompt_template=prompt_template,
            is_repeat=is_repeat,
            state=state,
            max_workers=None  # 自动选择线程数
        )
    else:
        # 使用原始的串行处理（保留作为备用方案）
        logger.info("ICRC Extractor: Using serial processing (concurrent processing disabled)")
        visit_results, visit_count, successful_visits, recordContentMap = process_visits_serially(
            visits=state.app_params.metaInfo.visits,
            target_field_names=target_field_names,
            target_fields_str=target_fields_str,
            target_fields_schema_str=target_fields_schema_str,
            knowledge_context=knowledge_context,
            prompt_template=prompt_template,
            is_repeat=is_repeat,
            state=state
        )

    visit_process_elapsed_time = time.time() - visit_process_start_time
    logger.info(f"TRACE - 结束处理visit数据 - 总耗时: {visit_process_elapsed_time:.4f}秒")

    # --- 5. Update State with Extracted Data ---
    logger.info(f"--- Finalizing ICRC Form Data Extraction ({len(visit_results)} potential results) ---")
    logger.info("TRACE - 开始更新状态和生成最终结果")
    final_process_start_time = time.time()

    final_table_list: List[CrfTable] = []
    total_filled_fields_count = 0

    # Helper function to find record_id by hit_value
    def find_record_id_by_hit_value(hit_value: str, record_content_map: Dict[str, str]) -> Optional[str]:
        """根据hitValue在recordContentMap中查找对应的record_id"""
        if not hit_value or not hit_value.strip():
            return None
        
        hit_value_content = hit_value.strip()
        logger.info(f"动态查找记录ID - 开始查找hitValue: '{hit_value_content}'")
        
        for rec_id, rec_content in record_content_map.items():
            if hit_value_content in rec_content:
                logger.info(f"动态查找记录ID - 匹配成功: hitValue='{hit_value_content}' 映射到 record_id={rec_id}")
                return rec_id
        
        logger.warning(f"动态查找记录ID - 匹配失败: hitValue='{hit_value_content}' 在所有记录中未找到")
        return None

    # Helper function to find data_item details by record_id
    def find_data_item_for_record(target_record_id: str) -> Optional[Tuple[Any, Any]]:
        for v in state.app_params.metaInfo.visits:
            for di in v.data:
                for r in di.records:
                    if r.recordId == target_record_id:
                        return v, di  # Return the visit and data_item containing the record
        return None, None

    if is_repeat == 1:
        # Repeat Form: Each result_item potentially creates a new table row
        logger.info("TRACE - 开始处理循环表单结果")
        repeat_start_time = time.time()

        logger.info("Processing results for REPEAT form (isRepeat=1)")
        for item_index, result_item in enumerate(visit_results):
            if "error" in result_item:
                logger.warning(f"Skipping error item: {result_item}")
                continue

            # Ensure essential keys are present
            if '__found_record_id__' not in result_item or '__visit_id__' not in result_item:
                logger.warning(
                    f"Skipping result item due to missing '__found_record_id__' or '__visit_id__': {result_item}")
                continue

            item_table = copy.deepcopy(target_table)  # Create a new table instance for this item
            item_table.visitId = str(result_item['__visit_id__'])
            item_filled_count = 0
            found_record_id = result_item['__found_record_id__']

            # Find the associated visit and data_item for DataSource details
            original_visit, original_data_item = find_data_item_for_record(found_record_id)

            # 为当前result_item创建独立的used_keys集合
            used_keys = set()

            for index_item in item_table.crfIndexList:
                field_name = index_item.crfIndexName

                # 使用新的查找函数处理重复键重命名，传入used_keys以确保按顺序匹配
                value, actual_key, hit_value_key = find_field_value_and_hit_value(result_item, field_name, used_keys)

                if value is not None:
                    # Find the corresponding hit_value for this specific field
                    field_hit_value = result_item.get(hit_value_key)

                    index_item.crfIndexValue = str(value)  # Ensure value is string
                    item_filled_count += 1
                    total_filled_fields_count += 1

                    # 只有当field_hit_value不为空时才创建DataSource
                    if field_hit_value is not None and field_hit_value.strip():
                        # 动态查找该字段对应的record_id
                        field_record_id = find_record_id_by_hit_value(field_hit_value, recordContentMap)
                        if field_record_id and field_record_id in recordContentMap:
                            # 使用字段特定的record_id和原文进行溯源
                            field_original_text = recordContentMap[field_record_id]
                            field_original_visit, field_original_data_item = find_data_item_for_record(field_record_id)
                            
                            startPointIndex = 0
                            endPointIndex = 0
                            logger.info(f"溯源前 - 字段: '{field_name}', hitValue: '{field_hit_value}', 长度: {len(field_hit_value)}")
                            logger.info(f"溯源前 - 使用record_id: {field_record_id}")
                            logger.info(f"溯源前 - field_original_text: '{field_original_text}', 长度: {len(field_original_text)}")
                            logger.info(f"溯源前 - hitValue十六进制: {field_hit_value.encode('utf-8').hex()}")
                            logger.info(f"溯源前 - field_original_text十六进制: {field_original_text.encode('utf-8').hex()}")
                            
                            evidence_text, start_pos, end_pos = get_modify_evidence(field_hit_value, field_original_text)
                            
                            logger.info(f"溯源结果 - evidence_text: {evidence_text}")
                            logger.info(f"溯源结果 - start_pos: {start_pos}")
                            logger.info(f"溯源结果 - end_pos: {end_pos}")
                            
                            if evidence_text is not None:
                                startPointIndex = start_pos
                                endPointIndex = end_pos
                                logger.info(f"溯源成功 - 字段: '{field_name}', startPointIndex: {startPointIndex}, endPointIndex: {endPointIndex}")
                            else:
                                logger.warning(f"溯源失败 - 字段: '{field_name}', evidence_text为None但start_pos={start_pos}, end_pos={end_pos}")
                            
                            index_item.dataSource = [
                                DataSource(
                                    recordId=field_record_id,
                                    docId=field_original_data_item.docId if hasattr(field_original_data_item, 'docId') else None,
                                    tableName=field_original_data_item.tableName if hasattr(field_original_data_item,
                                                                                      'tableName') else None,
                                    tableNameDesc=field_original_data_item.tableNameDesc if hasattr(field_original_data_item,
                                                                                              'tableNameDesc') else None,
                                    hitValue=field_hit_value,
                                    startPointIndex=startPointIndex,
                                    endPointIndex=endPointIndex
                                )
                            ]
                        else:
                            # Fallback: 使用原来的found_record_id
                            logger.warning(f"字段 '{field_name}' 无法找到对应的record_id，使用回退record_id: {found_record_id}")
                            index_item.dataSource = [DataSource(recordId=found_record_id, hitValue=field_hit_value)]
                    else:
                        # 字段有值但没有hitValue，不创建dataSource
                        logger.info(f"字段 '{field_name}' 有值但没有hitValue，不创建dataSource")
                        index_item.dataSource = []
            # Only add the table if at least one field was filled for this item
            if item_filled_count > 0:
                final_table_list.append(item_table)
                logger.info(
                    f"Added table for repeat item {item_index} (Visit: {item_table.visitId}, Record: {found_record_id}) with {item_filled_count} fields.")
            else:
                logger.info(
                    f"Skipping empty table for repeat item {item_index} (Visit: {result_item.get('__visit_id__')}, Record: {found_record_id}).")

        repeat_elapsed_time = time.time() - repeat_start_time
        logger.info(f"TRACE - 结束处理循环表单结果 - 耗时: {repeat_elapsed_time:.4f}秒")

    else:  # is_repeat == 0
        # Non-Repeat Form: Accumulate data until one complete form is found
        logger.info("TRACE - 开始处理非循环表单结果")
        non_repeat_start_time = time.time()

        logger.info("Processing results for NON-REPEAT form (isRepeat=0)")
        # 改为列表结构，每个元素包含字段信息
        accumulated_data_list = []
        found_all_fields = False
        final_table_visit_id = None

        # 统计visit_results中每个元素的有效字段数量，找到最优的result_item
        def count_valid_fields(item):
            """统计result_item中有效字段的数量"""
            if not isinstance(item, dict):
                return 0

            valid_count = 0
            for key, value in item.items():
                # 排除特殊键（元数据键和hit_value键）
                if (key.endswith('_hit_value') or key == 'error'):
                    continue

                # 检查值是否为有效值（不为None且不为空字符串）
                if value is not None and str(value).strip() != "":
                    valid_count += 1

            return valid_count

        # 统计所有result_item的有效字段数量
        result_stats = []
        best_result_item = None
        max_valid_fields = 0

        logger.info(f"TRACE - 开始统计{len(visit_results)}个result_item的有效字段数量")
        for idx, result_item in enumerate(visit_results):
            if "error" in result_item:
                continue  # 跳过错误项

            valid_count = count_valid_fields(result_item)
            result_stats.append({
                'index': idx,
                'valid_fields': valid_count,
                'visit_id': result_item.get('__visit_id__', 'unknown'),
                'record_id': result_item.get('__found_record_id__', 'unknown')
            })

            # 更新最佳result_item
            if valid_count > max_valid_fields:
                max_valid_fields = valid_count
                best_result_item = result_item

        if final_table_visit_id is None:  # Capture the visit ID from the first valid item contributing
            final_table_visit_id = str(best_result_item['__visit_id__'])

        found_record_id = best_result_item['__found_record_id__']
        logger.info(f"最佳结果选择 - 有效字段数: {max_valid_fields}")
        logger.info(f"最佳结果选择 - recordContentMap中可用记录: {list(recordContentMap.keys())}")
        logger.info(f"最佳结果选择 - 注意：现在每个字段将动态查找自己的record_id，不再依赖统一的found_record_id")
        
        # 保留found_record_id仅用于回退场景
        original_visit, original_data_item = find_data_item_for_record(found_record_id)

        # 为当前result_item创建独立的used_keys集合
        used_keys = set()

        for field_name in target_field_names:
            # 使用新的查找函数处理重复键重命名，传入used_keys以确保按顺序匹配
            value, actual_key, hit_value_key = find_field_value_and_hit_value(best_result_item, field_name, used_keys)

            if value is not None:
                # Find the corresponding hit_value for this specific field
                field_hit_value = best_result_item.get(hit_value_key)

                # 只有当field_hit_value不为空时才创建DataSource
                datasource_list = []
                if field_hit_value is not None and field_hit_value.strip():
                    # 动态查找该字段对应的record_id
                    field_record_id = find_record_id_by_hit_value(field_hit_value, recordContentMap)
                    if field_record_id and field_record_id in recordContentMap:
                        # 使用字段特定的record_id和原文进行溯源
                        field_original_text = recordContentMap[field_record_id]
                        field_original_visit, field_original_data_item = find_data_item_for_record(field_record_id)
                        
                        startPointIndex = 0
                        endPointIndex = 0
                        logger.info(f"溯源前 - 字段: '{field_name}', hitValue: '{field_hit_value}', 长度: {len(field_hit_value)}")
                        logger.info(f"溯源前 - 使用record_id: {field_record_id}")
                        logger.info(f"溯源前 - field_original_text: '{field_original_text}', 长度: {len(field_original_text)}")
                        logger.info(f"溯源前 - hitValue十六进制: {field_hit_value.encode('utf-8').hex()}")
                        logger.info(f"溯源前 - field_original_text十六进制: {field_original_text.encode('utf-8').hex()}")
                        
                        evidence_text, start_pos, end_pos = get_modify_evidence(field_hit_value, field_original_text)
                        
                        logger.info(f"溯源结果 - evidence_text: {evidence_text}")
                        logger.info(f"溯源结果 - start_pos: {start_pos}")
                        logger.info(f"溯源结果 - end_pos: {end_pos}")
                        
                        if evidence_text is not None:
                            startPointIndex = start_pos
                            endPointIndex = end_pos
                            logger.info(f"溯源成功 - 字段: '{field_name}', startPointIndex: {startPointIndex}, endPointIndex: {endPointIndex}")
                        else:
                            logger.warning(f"溯源失败 - 字段: '{field_name}', evidence_text为None但start_pos={start_pos}, end_pos={end_pos}")
                        
                        datasource_list = [
                            DataSource(
                                recordId=field_record_id,
                                docId=field_original_data_item.docId if hasattr(field_original_data_item, 'docId') else None,
                                tableName=field_original_data_item.tableName if hasattr(field_original_data_item,
                                                                                  'tableName') else None,
                                tableNameDesc=field_original_data_item.tableNameDesc if hasattr(field_original_data_item,
                                                                                          'tableNameDesc') else None,
                                hitValue=field_hit_value,
                                startPointIndex=startPointIndex,
                                endPointIndex=endPointIndex
                            )
                        ]
                    else:
                        # Fallback: 使用原来的found_record_id
                        logger.warning(f"字段 '{field_name}' 无法找到对应的record_id，使用回退record_id: {found_record_id}")
                        datasource_list = [DataSource(recordId=found_record_id, hitValue=field_hit_value)]
                else:
                    # 字段有值但没有hitValue，不创建dataSource
                    logger.info(f"字段 '{field_name}' 有值但没有hitValue，不创建dataSource")
                    datasource_list = []
                    logger.warning(
                        f"Could not find original data_item for record_id {found_record_id} when creating DataSource for field {field_name}")

                # 添加到累积数据列表中
                accumulated_data_list.append({
                    'original_field_name': field_name,
                    'actual_key': actual_key,
                    'value': str(value),
                    'data_sources': datasource_list
                })
                total_filled_fields_count += 1

                logger.info(
                    f"Added field data: original_name='{field_name}', actual_key='{actual_key}', value='{str(value)[:50]}...'")
            else:
                # 值为None时，添加空值记录
                accumulated_data_list.append({
                    'original_field_name': field_name,
                    'actual_key': actual_key,
                    'value': "",  # 设为空字符串
                    'data_sources': []  # 设为空列表而不是None，保持数据结构一致
                })

                logger.info(
                    f"Added empty field data: original_name='{field_name}', actual_key='{actual_key}', value=''")

        # Create the single table if data was accumulated
        logger.info("TRACE - 开始创建非循环表单")
        create_table_start_time = time.time()

        if accumulated_data_list:
            final_table = copy.deepcopy(target_table)
            final_table.visitId = final_table_visit_id if final_table_visit_id else "unknown"
            item_filled_count = 0

            # 创建字段名到累积数据的映射，支持多个数据项对应同一个原始字段名
            field_data_map = {}
            for data_item in accumulated_data_list:
                original_field = data_item['original_field_name']
                if original_field not in field_data_map:
                    field_data_map[original_field] = []
                field_data_map[original_field].append(data_item)

            for index_item in final_table.crfIndexList:
                field_name = index_item.crfIndexName
                if field_name in field_data_map:
                    # 如果有多个数据项，取第一个（或者可以根据需要调整策略）
                    field_data = field_data_map[field_name].pop(0)
                    index_item.crfIndexValue = field_data['value']
                    index_item.dataSource = field_data['data_sources']
                    item_filled_count += 1

                    # 如果有多个数据项，记录警告
                    if len(field_data_map[field_name]) > 1:
                        logger.warning(f"Multiple data items found for field '{field_name}', using the first one. "
                                       f"Total items: {len(field_data_map[field_name])}")

            final_table_list.append(final_table)
            logger.info(
                f"Added single table for non-repeat form (Visit: {final_table.visitId}) with {item_filled_count} fields.")
        elif not visit_results:  # Handle case where visit_results was empty
            logger.warning("No valid results found to populate non-repeat form.")
        else:  # Handle case where loop finished but form not complete
            logger.warning("Could not accumulate all required fields for non-repeat form.")
            # Optionally create a partially filled table if desired
            # final_table = copy.deepcopy(target_table)
            # ... populate partial data ...
            # final_table_list.append(final_table)

        create_table_elapsed_time = time.time() - create_table_start_time
        logger.info(f"TRACE - 结束创建非循环表单 - 耗时: {create_table_elapsed_time:.4f}秒")

        non_repeat_elapsed_time = time.time() - non_repeat_start_time
        logger.info(f"TRACE - 结束处理非循环表单结果 - 耗时: {non_repeat_elapsed_time:.4f}秒")

    # --- Generate Final Response Message ---
    logger.info("TRACE - 开始生成最终响应消息")
    response_start_time = time.time()

    # 无论final_table_list是否为空，都按正常流程返回结果
    # final_table_list为0时，可能是匹配的字段本来就都没有找出来，这是正常情况
    if not final_table_list:
        # 当final_table_list为空时，为每个visit创建一个表单结构，将crfIndexValue设置为空字符串
        logger.info("TRACE - 开始为每个visit设置空字段值")
        empty_table_start_time = time.time()

        # 为每个visit创建一个表单结构
        if visits and len(visits) > 0:
            for visit in visits:
                # 使用入参的表单结构，将所有字段的crfIndexValue设置为空字符串
                visit_table = copy.deepcopy(target_table)
                for index_item in visit_table.crfIndexList:
                    index_item.crfIndexValue = ""  # 设置为空字符串

                # 设置当前visit的visitId
                visit_table.visitId = visit.visitId

                # 将表单添加到final_table_list中
                final_table_list.append(visit_table)
                logger.info(f"Created empty table for visitId: {visit.visitId}")
        else:
            # 如果没有visits，创建一个默认的空表单
            empty_table = copy.deepcopy(target_table)
            for index_item in empty_table.crfIndexList:
                index_item.crfIndexValue = ""  # 设置为空字符串
            empty_table.visitId = "unknown"
            final_table_list.append(empty_table)
            logger.info("Created empty table with visitId: unknown")

        empty_table_elapsed_time = time.time() - empty_table_start_time
        logger.info(f"TRACE - 结束为每个visit设置空字段值 - 耗时: {empty_table_elapsed_time:.4f}秒")

        # 根据处理情况生成相应的消息
        if successful_visits > 0:
            final_response_message = "AI已成功分析记录，但在当前数据中未找到与表单定义匹配的字段值。"
        elif visit_count > 0:
            final_response_message = "AI已完成记录分析，但未能从数据中提取到表单字段值。"
        else:
            final_response_message = "AI已完成分析，但没有找到可处理的访问记录。"
        logger.info(f"Created {len(final_table_list)} empty tables for {len(visits) if visits else 0} visits")
    else:
        num_tables = len(final_table_list)
        table_text = "表单" if num_tables == 1 else "表单"
        field_text = "字段" if total_filled_fields_count == 1 else "字段"
        processed_visits = len(set(t.visitId for t in final_table_list if t.visitId))
        visit_text = "电子源" if processed_visits == 1 else "电子源"

        if is_repeat == 1:
            final_response_message = f"成功提取数据，已从{processed_visits}个{visit_text}中创建{num_tables}个{table_text}，共填充{total_filled_fields_count}个{field_text}。"
        else:  # is_repeat == 0
            if found_all_fields:
                final_response_message = f"成功提取数据，已从{visit_text}{final_table_list[0].visitId}中创建1个完整{table_text}，共填充{total_filled_fields_count}个{field_text}。"
            else:
                final_response_message = f"已提取部分数据，从{visit_text}{final_table_list[0].visitId}中创建1个{table_text}，共填充{total_filled_fields_count}个{field_text}。未能找到所有必填字段。"

    logger.info(f"Final response: {final_response_message}")

    response_elapsed_time = time.time() - response_start_time
    logger.info(f"TRACE - 结束生成最终响应消息 - 耗时: {response_elapsed_time:.4f}秒")

    # 计算总耗时
    final_process_elapsed_time = time.time() - final_process_start_time
    logger.info(f"TRACE - 结束更新状态和生成最终结果 - 总耗时: {final_process_elapsed_time:.4f}秒")

    # Return the final state update
    # Ensure the key matches what the graph expects (e.g., 'icrc_form_data' or 'crf_result')
    # Based on original return, seems 'crf_result' might be expected by graph edge/conditional logic
    return {
        "thought_log": state.thought_log,
        "icrc_intent": state.icrc_intent,
        "crf_result": final_table_list,
        "final_response": final_response_message,
        "visit_results": visit_results,
        "model_name": os.getenv("LLM_MODEL_NAME"),
        "knowledge_slice": state.knowledge_slice,
        "llm_content": getattr(state, 'llm_content', None)
    }


@trace_time("转换知识切片")
def convert_to_knowledge_slice(doc_result):
    """
    将DocumentResult转换为KnowledgeSlice格式

    Args:
        doc_result: DocumentResult对象

    Returns:
        KnowledgeSlice对象
    """
    # 从metadata中获取source作为fileName
    file_name = doc_result.metadata.get("source", "未知文件")

    # 从metadata中获取doc_id作为fileId
    file_id = doc_result.metadata.get("doc_id", "")

    # 不创建Position对象，而是直接使用字典格式
    position = {
        "context": doc_result.content,
        "pageNo": doc_result.metadata.get("pageNo"),
        "start": doc_result.metadata.get("start", 0),
        "end": doc_result.metadata.get("end", 0)
    }

    # 创建并返回KnowledgeSlice对象
    return KnowledgeSlice(
        fileId=file_id,
        fileName=file_name,
        fileUrl=doc_result.metadata.get("fileUrl"),
        tag=1,  # 默认值
        start=doc_result.metadata.get("start", 0),
        end=doc_result.metadata.get("end", 0),
        context=doc_result.content,
        position=position
    )


@trace_time("检索知识库")
def recallKnowledge(knowledge, target_field_names, state=None):
    field_knowledge_map = {}
    knowledge_context = ""
    knowledge_slices = []  # 用于存储转换后的KnowledgeSlice对象

    # 处理用户上传的知识
    user_knowledge_context = ""
    if state and state.user_uploaded_knowledge and state.user_uploaded_knowledge_content:
        logger.info("TRACE - 开始处理用户上传的知识")
        user_start_time = time.time()

        # 打印详细信息以便调试
        logger.info(f"recallKnowledge: 用户上传知识数量: {len(state.user_uploaded_knowledge)}")
        logger.info(f"recallKnowledge: 用户上传知识内容映射键数量: {len(state.user_uploaded_knowledge_content)}")

        user_knowledge = state.user_uploaded_knowledge
        user_knowledge_content = state.user_uploaded_knowledge_content

        if user_knowledge and user_knowledge_content:
            user_knowledge_sections = []
            for k_file in user_knowledge:
                file_id = k_file.fileId
                logger.info(f"recallKnowledge: 处理知识文件 fileId={file_id}, fileName={k_file.fileName}")

                if file_id in user_knowledge_content:
                    content = user_knowledge_content[file_id]
                    logger.info(f"recallKnowledge: 找到知识内容，长度={len(content)}")

                    # 创建KnowledgeSlice对象
                    position = {
                        "context": content,
                        "pageNo": None,
                        "start": 0,
                        "end": len(content)
                    }

                    knowledge_slice = KnowledgeSlice(
                        fileId=file_id,
                        fileName=k_file.fileName,
                        fileUrl=k_file.fileUrl,
                        tag=1,
                        start=0,
                        end=len(content),
                        context=content,
                        position=position
                    )
                    knowledge_slices.append(knowledge_slice)
                    logger.info(f"recallKnowledge: 创建了KnowledgeSlice对象，添加到knowledge_slices")

                    # 为每个目标字段添加用户上传的知识
                    for field_name in target_field_names:
                        if field_name not in field_knowledge_map:
                            field_knowledge_map[field_name] = []
                        # 添加用户上传的知识到字段知识映射，包含文档标识
                        enhanced_user_content = f"[文档ID: {file_id}, 文件名: {k_file.fileName}]\n{content}"
                        field_knowledge_map[field_name].append(enhanced_user_content)
                        logger.info(f"recallKnowledge: 为字段'{field_name}'添加了用户上传的知识，包含文档溯源")

                    # 添加到用户知识上下文
                    escaped_content = content.replace('{', '{{').replace('}', '}}')
                    enhanced_escaped_content = f"[文档ID: {file_id}, 文件名: {k_file.fileName}]\n{escaped_content}"
                    user_knowledge_sections.append(f"## 用户上传知识\n{enhanced_escaped_content}")
                    logger.info(f"recallKnowledge: 添加到用户知识上下文")
                else:
                    logger.warning(f"recallKnowledge: 未找到知识内容: fileId={file_id}")

            if user_knowledge_sections:
                user_knowledge_context = "\n\n".join(user_knowledge_sections)
                logger.info(f"ICRC Extractor: 处理了{len(user_knowledge)}条用户上传的知识")
                logger.info(f"recallKnowledge: 用户知识上下文长度={len(user_knowledge_context)}")
            else:
                logger.warning("recallKnowledge: 没有生成用户知识上下文")
        else:
            logger.warning("recallKnowledge: user_knowledge或user_knowledge_content为空")

        user_elapsed_time = time.time() - user_start_time
        logger.info(f"TRACE - 结束处理用户上传的知识 - 耗时: {user_elapsed_time:.4f}秒")

    # 处理元信息中的知识库
    if knowledge and len(knowledge) > 0:
        try:
            # 初始化QueryService
            logger.info("TRACE - 开始初始化QueryService")
            init_start_time = time.time()
            query_service = QueryService()
            init_elapsed_time = time.time() - init_start_time
            logger.info(f"TRACE - 结束初始化QueryService - 耗时: {init_elapsed_time:.4f}秒")

            # 收集所有fileId作为doc_ids
            logger.info("TRACE - 开始收集fileId")
            collect_start_time = time.time()
            doc_ids = [k.fileId for k in knowledge if k.fileId]
            if not doc_ids:
                logger.warning("ICRC Extractor: No valid fileId found in knowledge")
            else:
                # 获取第一个knowledge的collectionName作为collection_name
                collection_name = knowledge[0].collectionName if knowledge[0].collectionName else "default"
            collect_elapsed_time = time.time() - collect_start_time
            logger.info(f"TRACE - 结束收集fileId - 耗时: {collect_elapsed_time:.4f}秒")

            if doc_ids:
                # 为每个字段单独检索
                logger.info("TRACE - 开始为每个字段检索知识库")
                for field_name in target_field_names:
                    field_start_time = time.time()
                    # 构建当前字段的查询问题
                    question = f"关于{field_name}的信息"
                    logger.info(f"ICRC Extractor: Querying knowledge base for field '{field_name}'")

                    # 构建查询请求
                    request = QueryRequest(
                        question=question,
                        collection_name=collection_name,
                        doc_ids=doc_ids
                    )

                    # 执行查询
                    query_start_time = time.time()
                    response = query_service.query(request)
                    query_elapsed_time = time.time() - query_start_time
                    logger.info(f"TRACE - 字段'{field_name}'查询耗时: {query_elapsed_time:.4f}秒")

                    # 处理查询结果
                    process_start_time = time.time()
                    if response and response.results and len(response.results) > 0:
                        # 初始化字段知识映射
                        if field_name not in field_knowledge_map:
                            field_knowledge_map[field_name] = []

                        # 将当前字段的检索结果添加到列表中，同时建立内容到文档信息的映射
                        field_contents = []
                        for result in response.results:
                            field_contents.append(result.content)
                            # 将每个DocumentResult转换为KnowledgeSlice并添加到列表中
                            knowledge_slice = convert_to_knowledge_slice(result)
                            knowledge_slices.append(knowledge_slice)

                        field_knowledge_map[field_name].extend(field_contents)

                        logger.info(
                            f"ICRC Extractor: Retrieved {len(response.results)} knowledge items for field '{field_name}'")
                    else:
                        logger.info(f"ICRC Extractor: No knowledge retrieved for field '{field_name}'")
                    process_elapsed_time = time.time() - process_start_time
                    logger.info(f"TRACE - 字段'{field_name}'结果处理耗时: {process_elapsed_time:.4f}秒")

                    field_elapsed_time = time.time() - field_start_time
                    logger.info(f"TRACE - 字段'{field_name}'总耗时: {field_elapsed_time:.4f}秒")

                # 将所有字段的知识整合到一起
                logger.info("TRACE - 开始整合知识")
                combine_start_time = time.time()
                if field_knowledge_map:
                    knowledge_sections = []
                    # 创建内容到文档信息的映射，提高查找效率
                    content_to_doc_info = {}
                    for knowledge_slice in knowledge_slices:
                        if knowledge_slice.context and knowledge_slice.fileId:
                            content_to_doc_info[knowledge_slice.context] = {
                                'doc_id': knowledge_slice.fileId,
                                'file_name': knowledge_slice.fileName or "未知文件"
                            }

                    # 为每个字段整合知识，并添加文档溯源信息
                    for field, contents in field_knowledge_map.items():
                        if contents:
                            # 处理知识内容，为每个片段添加文档来源标识
                            enhanced_contents = []

                            for content in contents:
                                if content.startswith("[文档ID:"):
                                    # 已经包含文档标识的内容（用户上传的知识或已处理的知识库内容）保持原样
                                    enhanced_contents.append(content)
                                else:
                                    # 为知识库检索的内容添加文档来源标识
                                    doc_info = content_to_doc_info.get(content)
                                    if doc_info:
                                        doc_id = doc_info['doc_id']
                                        file_name = doc_info['file_name']
                                        # 添加文档来源标识
                                        enhanced_content = f"[文档ID: {doc_id}, 文件名: {file_name}]\n{content}"
                                        enhanced_contents.append(enhanced_content)
                                        logger.info(
                                            f"为字段'{field}'的知识片段添加了文档溯源: doc_id={doc_id}, fileName={file_name}")
                                    else:
                                        # 如果找不到对应的文档信息，保持原内容
                                        enhanced_contents.append(content)
                                        logger.warning(f"未找到字段'{field}'的知识片段对应的文档信息")

                            # 将增强后的内容合并为字符串
                            field_content = "\n\n".join(enhanced_contents)
                            if field_content.strip():
                                # Escape curly braces in content to prevent misinterpretation by LangChain
                                escaped_content = field_content.replace('{', '{{').replace('}', '}}')
                                knowledge_sections.append(f"## {field}\n{escaped_content}")

                    # 如果有用户上传的知识，添加到知识上下文的开头
                    if user_knowledge_context:
                        knowledge_sections.insert(0, user_knowledge_context)

                    knowledge_context = "\n\n".join(knowledge_sections)
                    logger.info(
                        f"ICRC Extractor: Created enhanced knowledge context with {len(field_knowledge_map)} fields and document traceability")
                    logger.info(
                        f"ICRC Extractor: Created {len(knowledge_slices)} knowledge slices with {len(content_to_doc_info)} content mappings")
                elif user_knowledge_context:
                    # 如果没有从知识库检索到内容，但有用户上传的知识，则使用用户上传的知识
                    knowledge_context = user_knowledge_context
                    logger.info("ICRC Extractor: Using only user uploaded knowledge")
                else:
                    logger.info("ICRC Extractor: No knowledge retrieved for any field")
                combine_elapsed_time = time.time() - combine_start_time
                logger.info(f"TRACE - 结束整合知识 - 耗时: {combine_elapsed_time:.4f}秒")
        except Exception as e:
            logger.error(f"ICRC Extractor: Error retrieving knowledge: {e}", exc_info=True)
            # 出错时继续执行，不影响主流程
    elif user_knowledge_context:
        # 如果没有元信息中的知识，但有用户上传的知识，则使用用户上传的知识
        knowledge_context = user_knowledge_context
        logger.info("ICRC Extractor: Using only user uploaded knowledge (no metadata knowledge)")

    # 返回知识上下文和知识切片列表
    return knowledge_context, knowledge_slices


@trace_time("处理Refill_Form")
def handle_refill_form(state: AssistantState) -> Dict[str, Any]:
    """
    处理Refill_Form意图，根据form_name调用不同的模块

    Args:
        state: 当前助手状态

    Returns:
        Dict[str, Any]: 处理结果
    """
    logger.info("--- 执行 Refill_Form 处理 ---")

    # 获取意图详情
    if not state.icrc_extracted_details:
        logger.warning("Refill_Form处理: 缺少意图详情")
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": "无法处理请求，缺少必要的表单信息。",
            "model_name": os.getenv("LLM_MODEL_NAME")
        }

    # 获取form_name和table_code
    form_name = state.icrc_source
    # 直接从state中获取table_code，与获取form_name的方式保持一致
    table_code = state.table_code if hasattr(state, 'table_code') else None
    logger.info(f"Refill_Form处理: 表单类型 = {form_name}, table_code = {table_code}")

    # 获取时间实体
    time_entities = state.icrc_extracted_details.time_entities if state.icrc_extracted_details else []

    # 准备参数
    # 从icrc_extracted_details中获取必要参数
    extracted_pts_id = state.icrc_extracted_details.subject_id if state.icrc_extracted_details else None

    # 如果state中没有table_code，则尝试从icrc_extracted_details中获取
    if table_code is None and state.icrc_extracted_details and hasattr(state.icrc_extracted_details, 'table_code'):
        table_code = state.icrc_extracted_details.table_code
        logger.info(f"从icrc_extracted_details获取table_code: {table_code}")

    # 然后从metaInfo中获取必要的参数
    # 首先检查是否有context.patientInfo
    if (hasattr(state.app_params.metaInfo, 'context') and
            state.app_params.metaInfo.context and
            hasattr(state.app_params.metaInfo.context, 'patientInfo') and
            state.app_params.metaInfo.context.patientInfo):
        # 从context.patientInfo获取
        patient_info = state.app_params.metaInfo.context.patientInfo
        pts_id = extracted_pts_id or patient_info.ptsId
        pro_id = patient_info.projectId
        empi_id = patient_info.empiId
        hospital_no = patient_info.hospitalNo
    else:
        # 从metaInfo直接获取
        pts_id = extracted_pts_id or state.app_params.metaInfo.ptsId
        pro_id = state.app_params.metaInfo.projectId
        empi_id = getattr(state.app_params.metaInfo, 'empiId', None)
        hospital_no = getattr(state.app_params.metaInfo, 'hospitalNo', None)

    logger.info(f"获取参数: pts_id={pts_id}, empi_id={empi_id}, pro_id={pro_id}, hospital_no={hospital_no}")

    # 默认时间参数
    start_time = None
    end_time = None
    biz_start_time = None
    biz_end_time = None
    time_type = None

    # 处理时间实体
    for entity in time_entities:
        # 确定时间类型
        biz_time_type = entity.biz_time_type if hasattr(entity, 'biz_time_type') else "业务时间"

        # 获取time_type
        entity_time_type = entity.time_type if hasattr(entity, 'time_type') and entity.time_type is not None else None
        if entity_time_type is not None:
            # 如果实体中已经有time_type，直接使用
            time_type = entity_time_type
            logger.info(f"从实体中获取time_type: {time_type}")
        elif entity.type == "relative_time" and entity.value == "最近一次":
            # 如果是"最近一次"，设置为1
            time_type = 1
            logger.info("检测到'最近一次'，设置time_type=1")
        elif entity.type == "date_range" and isinstance(entity.value, dict):
            # 如果是日期范围，设置为2
            time_type = 2
            logger.info("检测到日期范围，设置time_type=2")

        # 根据时间类型设置不同的参数
        if biz_time_type == "就诊时间":
            # 就诊时间使用start_time和end_time
            if entity.type == "date_point":
                start_time = entity.value
                end_time = entity.value
            elif entity.type == "date_range" and isinstance(entity.value, dict):
                start_time = entity.value.get("start_time")
                end_time = entity.value.get("end_time")
        elif biz_time_type == "业务时间":
            # 业务时间使用biz_start_time和biz_end_time
            if entity.type == "date_point":
                biz_start_time = entity.value
                biz_end_time = entity.value
            elif entity.type == "date_range" and isinstance(entity.value, dict):
                biz_start_time = entity.value.get("start_time")
                biz_end_time = entity.value.get("end_time")
        elif biz_time_type == "首次访视时间":
            # 首次访视时间需要调用baseline_visit.py
            try:
                # 导入baseline_visit模块
                baseline_visit_module = importlib.import_module("app.function_call.baseline_visit")

                # 调用API函数获取首次访视时间
                if pts_id:
                    baseline_result = baseline_visit_module.get_baseline_visit_date_api({"pts_id": pts_id})

                    if baseline_result.get("success") and baseline_result.get("data"):
                        baseline_date = baseline_result["data"].get("baseline_visit_date")
                        if baseline_date:
                            biz_start_time = baseline_date
                            biz_end_time = baseline_date
                            logger.info(f"获取到首次访视时间: {baseline_date}")
                        else:
                            logger.warning(f"未找到患者 {pts_id} 的首次访视时间")
                    else:
                        logger.warning(f"获取首次访视时间失败: {baseline_result.get('message')}")
                else:
                    logger.warning("缺少pts_id参数，无法获取首次访视时间")
            except Exception as e:
                logger.error(f"调用baseline_visit模块出错: {str(e)}")

    # 根据form_name调用不同的模块
    result = None

    try:
        if form_name == "Structure":
            # 调用structure.py
            logger.info("调用structure.py处理结构化数据")

            # 准备参数
            # 直接使用从state中获取的table_code，如果没有则使用默认值
            params = {
                "table_code": table_code,  # 优先使用从state中获取的table_code
                "empi_id": empi_id,
                "pts_id": pts_id,
                "pro_id": pro_id,
                "hospital_no": hospital_no,
                "time_type": time_type,
                "start_time": start_time,
                "end_time": end_time,
                "biz_start_time": biz_start_time,
                "biz_end_time": biz_end_time
            }
            logger.info(f"调用structure.py使用的table_code: {params['table_code']}")

            # 过滤掉None值
            filtered_params = {k: v for k, v in params.items() if v is not None}

            # 调用workflow_function
            result = structure.workflow_function(**filtered_params)

        elif form_name == "Paper":
            # 调用paper.py
            logger.info("调用paper.py处理纸质文件数据")

            # 准备参数
            # 将state.app_params.questions中role为"user"的content字符串全部拼接在一起
            natural_language = ""
            for msg in state.app_params.questions:
                if msg.role == "user":
                    natural_language += msg.content + " "
            natural_language = natural_language.strip()  # 去除末尾多余空格
            logger.info(f"拼接后的natural_language: {natural_language[:100]}...")  # 记录日志，只显示前100个字符

            # 获取design_version_id
            design_version_id = state.app_params.metaInfo.context.patientInfo.designVersionId

            # 直接使用从state中获取的table_code，如果没有则使用默认值
            default_table_code = state.app_params.metaInfo.crfInfo.tableList[
                0].crfTableName if state.app_params.metaInfo.crfInfo.tableList else None
            params = {
                "naturalLanguage": natural_language,
                "pts_id": pts_id,
                "pro_id": pro_id,
                "design_version_id": design_version_id,
                "table_code": table_code or default_table_code,  # 优先使用从state中获取的table_code
                "time_type": time_type,
                "start_time": start_time,
                "end_time": end_time,
                "biz_start_time": biz_start_time,
                "biz_end_time": biz_end_time
            }
            logger.info(f"调用paper.py使用的table_code: {params['table_code']}")

            # 过滤掉None值
            filtered_params = {k: v for k, v in params.items() if v is not None}

            # 调用transform_paper_entity_api
            result = paper.transform_paper_entity_api(filtered_params)

        elif form_name == "Medical_Record":
            # 预留接口，暂不做操作
            logger.info("Medical_Record类型暂不支持")
            return {
                "thought_log": getattr(state, 'thought_log', []),
                "icrc_intent": state.icrc_intent,
                "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
                "crf_result": [],
                "final_response": "病历类型的表单重填功能暂未实现，请稍后再试。",
                "model_name": os.getenv("LLM_MODEL_NAME"),
                "table_code": table_code  # 添加table_code字段
            }
        elif form_name == "未指定表单":
            # 处理"未指定表单"的情况，直接使用入参中的visits数据
            logger.info("检测到'未指定表单'，直接使用入参中的visits数据")

            # 直接从state.app_params.metaInfo中获取visits数据
            visits = state.app_params.metaInfo.visits

            if not visits:
                logger.warning("入参中没有visits数据")
                return {
                    "thought_log": getattr(state, 'thought_log', []),
                    "icrc_intent": state.icrc_intent,
                    "icrc_extracted_details": state.icrc_extracted_details,
                    "crf_result": [],
                    "final_response": "未找到访视数据，请检查后重试。",
                    "model_name": os.getenv("LLM_MODEL_NAME"),
                    "table_code": table_code
                }

            # 将visits数据转换为字典格式，以便返回
            visits_data = []
            for visit in visits:
                visit_dict = {
                    "visitId": visit.visitId,
                    "visitType": visit.visitType,
                    "data": []
                }

                for data_item in visit.data:
                    data_dict = {
                        "docId": data_item.docId,
                        "isStruct": data_item.isStruct,
                        "tableNameDesc": data_item.tableNameDesc,
                        "tableName": data_item.tableName,
                        "records": []
                    }

                    for record in data_item.records:
                        record_dict = {
                            "content": record.content,
                            "recordId": record.recordId
                        }
                        data_dict["records"].append(record_dict)

                    visit_dict["data"].append(data_dict)

                visits_data.append(visit_dict)

            logger.info(f"成功从入参中获取到 {len(visits_data)} 个visits数据")

            # 构建返回结果，包含visits数据
            return {
                "thought_log": getattr(state, 'thought_log', []),
                "icrc_intent": state.icrc_intent,
                "icrc_extracted_details": state.icrc_extracted_details,
                "crf_result": [],
                "final_response": "成功获取访视信息，将继续处理表单填写。",
                "model_name": os.getenv("LLM_MODEL_NAME"),
                "visits": visits_data,  # 添加visits数据
                "table_code": table_code
            }
        else:
            logger.warning(f"未知的表单类型: {form_name}")
            return {
                "thought_log": getattr(state, 'thought_log', []),
                "icrc_intent": state.icrc_intent,
                "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
                "crf_result": [],
                "final_response": f"不支持的表单类型: {form_name}",
                "model_name": os.getenv("LLM_MODEL_NAME"),
                "table_code": table_code  # 添加table_code字段
            }
    except Exception as e:
        logger.error(f"处理Refill_Form时发生错误: {str(e)}")
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": f"处理请求时发生错误: {str(e)}",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 处理结果
    if not result:
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": "未能获取有效数据，请检查参数后重试。",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 检查结果是否成功
    if result.get("success") == False:
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "visits": [],
            "final_response": f"获取数据失败: {result.get('message')}",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 提取结果中的数据
    transform_results = result.get("transform_results")

    if not transform_results:
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": "转换数据失败，未获取到有效结果。",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 构建返回结果
    return {
        "thought_log": getattr(state, 'thought_log', []),
        "icrc_intent": state.icrc_intent,
        "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details，包含table_code
        "crf_result": transform_results.get("data", []),
        "final_response": f"成功获取并转换数据: {result.get('message')}",
        "model_name": os.getenv("LLM_MODEL_NAME"),
        "visits": result.get("visits").get("data"),  # 添加visits数据
        "table_code": table_code  # 直接添加table_code字段
    }


@trace_time("并发处理visits")
def process_visits_concurrently(visits, target_field_names, target_fields_str, target_fields_schema_str,
                                knowledge_context, prompt_template, is_repeat, state, max_workers=None):
    """
    并发处理多个visits的数据提取

    Args:
        visits: visits列表
        target_field_names: 目标字段名列表
        target_fields_str: 目标字段字符串
        target_fields_schema_str: 目标字段schema字符串
        knowledge_context: 知识上下文
        prompt_template: 提示词模板
        is_repeat: 是否为循环表单
        state: 当前状态
        max_workers: 最大线程数，默认为None（自动选择）

    Returns:
        Tuple: (visit_results, visit_count, successful_visits, recordContentMap)
    """
    logger.info("TRACE - 开始并发处理visits")
    concurrent_start_time = time.time()

    visit_results = []
    visit_count = 0
    successful_visits = 0
    recordContentMap = {}

    # 如果没有指定最大线程数，根据visits数量和CPU核心数自动选择
    if max_workers is None:
        import os
        cpu_count = os.cpu_count() or 4
        max_workers = min(len(visits), cpu_count * 2, 10)  # 限制最大线程数为10

    logger.info(f"TRACE - 使用{max_workers}个线程并发处理{len(visits)}个visits")

    # 使用ThreadPoolExecutor进行并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有visit处理任务
        future_to_visit = {}
        for visit in visits:
            future = executor.submit(
                process_single_visit,
                visit, target_field_names, target_fields_str, target_fields_schema_str,
                knowledge_context, prompt_template, is_repeat, state
            )
            future_to_visit[future] = visit

        logger.info(f"TRACE - 已提交{len(future_to_visit)}个visit处理任务")

        # 收集结果
        completed_visits = []
        for future in as_completed(future_to_visit):
            visit = future_to_visit[future]
            try:
                result = future.result()
                completed_visits.append(result)

                # 更新统计信息
                if result["processed"]:
                    successful_visits += 1

                # 累计visit_count（基于处理的data_item数量）
                visit_count += len(result["results"])

                # 合并结果
                visit_results.extend(result["results"])

                # 合并记录内容映射
                recordContentMap.update(result["record_content_updates"])

                logger.info(f"TRACE - 完成visit {result['visit_id']} 处理，线程{result['thread_id']}，"
                            f"耗时{result['processing_time']:.4f}秒，结果数量{len(result['results'])}")

                # 对于非循环表单，记录是否找到完整表单（仅用于日志记录）
                if is_repeat == 0 and result["found_complete_form"]:
                    logger.info(f"TRACE - visit {result['visit_id']} 找到完整的非循环表单数据")

            except Exception as e:
                logger.error(f"TRACE - 处理visit {visit.visitId} 时发生异常: {e}")
                # 添加错误结果
                visit_results.append({"error": str(e), "visit_id": visit.visitId})

    concurrent_elapsed_time = time.time() - concurrent_start_time
    logger.info(f"TRACE - 结束并发处理visits - 总耗时: {concurrent_elapsed_time:.4f}秒")
    logger.info(f"TRACE - 并发处理统计: 总visits={len(visits)}, 成功visits={successful_visits}, "
                f"总结果数={len(visit_results)}, 记录映射数={len(recordContentMap)}")

    return visit_results, visit_count, successful_visits, recordContentMap


@trace_time("串行处理visits")
def process_visits_serially(visits, target_field_names, target_fields_str, target_fields_schema_str,
                            knowledge_context, prompt_template, is_repeat, state):
    """
    串行处理多个visits的数据提取（原始实现的备用方案）

    Args:
        visits: visits列表
        target_field_names: 目标字段名列表
        target_fields_str: 目标字段字符串
        target_fields_schema_str: 目标字段schema字符串
        knowledge_context: 知识上下文
        prompt_template: 提示词模板
        is_repeat: 是否为循环表单
        state: 当前状态

    Returns:
        Tuple: (visit_results, visit_count, successful_visits, recordContentMap)
    """
    logger.info("TRACE - 开始串行处理visits")
    serial_start_time = time.time()

    visit_results = []
    visit_count = 0
    successful_visits = 0
    recordContentMap = {}

    # 遍历每个visit
    for visit in visits:
        visit_id = visit.visitId
        logger.info(f"TRACE - 开始处理visit: {visit_id}")
        single_visit_start_time = time.time()

        visit_data = visit.data
        # Store results associated with this visit
        current_visit_results = []
        visit_processed = False
        found_complete_form = False  # For non-repeat forms

        for data_item in visit_data:
            logger.info(f"TRACE - 开始处理data_item")
            data_item_start_time = time.time()

            records = data_item.records
            # 处理记录内容
            record_contents_map, combined_content_parts, record_content_updates = process_record_contents(records)
            # 更新全局记录内容映射
            recordContentMap.update(record_content_updates)

            # Skip if no valid content in this data_item
            if not combined_content_parts:
                logger.info("TRACE - 跳过无有效内容的data_item")
                continue

            # Combine content for LLM input
            source_text = "\n\n".join(combined_content_parts)

            # If non-repeat and already found complete data, skip further processing for this visit
            if is_repeat == 0 and found_complete_form:
                logger.info(
                    f"ICRC Extractor: Non-repeat form with complete data found for visit {visit_id}, skipping remaining data_items")
                break  # Break data_item loop for this visit

            visit_count += 1  # Increment count per data_item processed
            logger.info(
                f"ICRC Extractor: Processing visit {visit_id}, combined content from {len(record_contents_map)} records.")

            # --- Prepare and Invoke LLM for the combined content ---
            logger.info("TRACE - 开始准备LLM输入")
            llm_prep_start_time = time.time()

            # Prepare the input dictionary for the prompt template
            if not knowledge_context:
                current_knowledge_context = "未提供"
            else:
                current_knowledge_context = knowledge_context

            # 预处理时间实体
            current_knowledge_context = preprocess_time_entities(current_knowledge_context, source_text)

            prompt_input = {
                "target_fields_list_placeholder": target_fields_str,
                "source_text": source_text,
                "target_fields_schema": target_fields_schema_str,
                "knowledge_context": current_knowledge_context
            }

            llm_prep_elapsed_time = time.time() - llm_prep_start_time
            logger.info(f"TRACE - 结束准备LLM输入 - 耗时: {llm_prep_elapsed_time:.4f}秒")

            # --- Direct API Invocation ---
            logger.info("TRACE - 开始生成提示词")
            prompt_gen_start_time = time.time()
            # First generate the prompt
            prompt_value = prompt_template.invoke(prompt_input)
            prompt_gen_elapsed_time = time.time() - prompt_gen_start_time
            logger.info(f"TRACE - 结束生成提示词 - 耗时: {prompt_gen_elapsed_time:.4f}秒")

            try:
                # 将 PromptValue 对象转换为文本
                logger.info("TRACE - 开始转换提示词为文本")
                prompt_convert_start_time = time.time()
                prompt_text = prompt_value.to_string()
                logger.info(f"Rendered prompt text: {prompt_text[:100]}...")
                prompt_convert_elapsed_time = time.time() - prompt_convert_start_time
                logger.info(f"TRACE - 结束转换提示词为文本 - 耗时: {prompt_convert_elapsed_time:.4f}秒")

                # 调用LLM进行提取
                extracted_results, success = call_llm_for_extraction(prompt_text, is_repeat, state)

                if not success:
                    logger.warning(f"ICRC Extractor: LLM extraction failed for visit {visit_id}.")
                    extracted_results = []

                # --- 处理提取结果并映射记录ID ---
                processed_results = process_extraction_results(extracted_results, record_contents_map, visit_id)
                current_visit_results.extend(processed_results)

                # Check for completeness if non-repeat form (is_repeat=0)
                logger.info("TRACE - 开始检查表单完整性")
                check_complete_start_time = time.time()
                if is_repeat == 0 and extracted_results:  # Check if we got any result
                    # Assuming the first result is the one to check for completeness
                    first_result = extracted_results[0]
                    all_fields_present = all(field in first_result for field in target_field_names)
                    if all_fields_present:
                        found_complete_form = True
                        logger.info(f"ICRC Extractor: Found complete data for non-repeat form in visit {visit_id}.")
                check_complete_elapsed_time = time.time() - check_complete_start_time
                logger.info(f"TRACE - 结束检查表单完整性 - 耗时: {check_complete_elapsed_time:.4f}秒")

                visit_processed = True  # Mark visit as processed if LLM call succeeded
                successful_visits += 1

            except Exception as e:
                logger.error(f"ICRC Extractor: LLM chain invocation failed for visit {visit_id}, data_item. Error: {e}")
                # Optionally add partial/error state to results
                current_visit_results.append({"error": str(e), "visit_id": visit_id})

            data_item_elapsed_time = time.time() - data_item_start_time
            logger.info(f"TRACE - 结束处理data_item - 耗时: {data_item_elapsed_time:.4f}秒")

        # --- Aggregate results for the entire visit ---
        logger.info("TRACE - 开始聚合visit结果")
        aggregate_start_time = time.time()
        if current_visit_results:
            visit_results.extend(current_visit_results)  # Add results from this visit
        elif not visit_processed:
            # Handle visits with no processable data_items or where all failed
            logger.warning(f"ICRC Extractor: No data successfully processed or extracted for visit {visit_id}.")
        aggregate_elapsed_time = time.time() - aggregate_start_time
        logger.info(f"TRACE - 结束聚合visit结果 - 耗时: {aggregate_elapsed_time:.4f}秒")

        single_visit_elapsed_time = time.time() - single_visit_start_time
        logger.info(f"TRACE - 结束处理visit: {visit_id} - 耗时: {single_visit_elapsed_time:.4f}秒")

    serial_elapsed_time = time.time() - serial_start_time
    logger.info(f"TRACE - 结束串行处理visits - 总耗时: {serial_elapsed_time:.4f}秒")
    logger.info(f"TRACE - 串行处理统计: 总visits={len(visits)}, 成功visits={successful_visits}, "
                f"总结果数={len(visit_results)}, 记录映射数={len(recordContentMap)}")

    return visit_results, visit_count, successful_visits, recordContentMap


@trace_time("预处理时间实体")
def preprocess_time_entities(knowledge_context: str, source_text: str) -> str:
    """
    预处理时间实体：如果knowledge_context包含特定标识，则提取时间实体并添加到上下文中
    
    Args:
        knowledge_context: 知识上下文
        source_text: 源文本
        
    Returns:
        str: 处理后的知识上下文
    """
    # 检查是否需要进行时间实体提取
    if "[二车间-提取时间实体]" not in knowledge_context:
        return knowledge_context
    
    logger.info("TRACE - 检测到时间实体提取标识，开始提取时间实体")
    
    try:
        # 调用时间实体提取接口
        time_entities = get_extract_ents_v2(text=source_text, ent_type="tim")
        
        if time_entities and len(time_entities) > 0:
            # 将时间实体列表转换为字符串格式
            time_entities_str = str(time_entities)
            
            # 替换知识上下文中的时间实体标识符
            enhanced_knowledge_context = knowledge_context.replace("[二车间-提取时间实体]", time_entities_str)
            
            logger.info(f"TRACE - 成功提取到{len(time_entities)}个时间实体: {time_entities}")
            logger.info(f"TRACE - 已将时间实体标识符替换为指导文本")
            
            return enhanced_knowledge_context
        else:
            logger.warning("TRACE - 未提取到任何时间实体")
            # 如果没有提取到时间实体，移除标识符
            return knowledge_context.replace("[二车间-提取时间实体]", "")
            
    except Exception as e:
        logger.error(f"TRACE - 时间实体提取失败: {str(e)}")
        # 出错时返回原始知识上下文，不影响主流程
        return knowledge_context

