"""
流程控制节点 - 负责决定流程的下一步
"""
import logging
from typing import Literal, Union

from app.domain.schemas import AssistantState

# 配置日志
logger = logging.getLogger(__name__)

def should_continue(state: AssistantState) -> str:
    """决定流程的下一步"""
    intent = state.intent
    adcode = state.adcode
    error = state.error_message

    # If an error occurred previously, go straight to response generation
    if error:
        logger.warning(f"Error detected ('{error}'), routing to generate_response.")
        return "generate_response"

    if intent == "time_query":
        # If the intent is to get the time, route to the action node to execute the tool
        logger.info("Intent is time_query, routing to run_time_tool.")
        return "run_time_tool"

    if intent == "weather_query":
        # If we have the adcode, query weather. If not, need district first.
        if adcode:
            logger.info("Intent is weather_query and adcode exists, routing to query_weather.")
            return "query_weather"
        else:
            logger.info("Intent is weather_query but no adcode yet, routing to query_district.")
            return "query_district"
    elif intent == "district_query":
        logger.info("Intent is district_query, routing to query_district.")
        return "query_district"
    elif intent == "user_query":
        logger.info("Intent is user_query, routing to query_user_action.")
        return "query_user"
    else:
        # Unknown intent or intent that doesn't require external tools
        logger.warning(f"Unknown or direct intent ('{intent}'), routing to generate_response.")
        return "generate_response"
