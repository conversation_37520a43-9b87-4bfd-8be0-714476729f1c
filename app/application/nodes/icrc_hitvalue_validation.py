"""
ICRC hitValue质检模块
专门用于检测和修正溯源失败的hitValue幻觉问题
"""

import logging
import os
from pathlib import Path
from typing import Optional

from app.domain.schemas import AssistantState, DataSource
from app.utils.direct_llm import call_llm_api
from app.utils.duplicate_key_json_parser import DuplicateKeyJsonParser
from app.utils.changsanyuan.run_get_modify_evidence import get_modify_evidence

logger = logging.getLogger(__name__)

# 提示词文件路径
PROMPT_FILE_PATH = os.path.join(
    os.path.dirname(__file__), 
    "icrc_hitvalue_validation_prompt.md"
)


def trace_time(func_name: str = None):
    """
    装饰器：记录函数执行时间和添加trace日志
    """
    import time
    from functools import wraps
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            name = func_name or func.__name__
            start_time = time.time()
            logger.info(f"TRACE - 开始执行: {name}")
            
            try:
                result = func(*args, **kwargs)
                elapsed_time = time.time() - start_time
                logger.info(f"TRACE - 结束执行: {name} - 耗时: {elapsed_time:.4f}秒")
                return result
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"TRACE - 执行异常: {name} - 耗时: {elapsed_time:.4f}秒 - 错误: {str(e)}")
                raise
        return wrapper
    return decorator


@trace_time("hitValue质检")
def icrc_hitvalue_validator(state: AssistantState) -> AssistantState:
    """
    对溯源失败的字段进行LLM质检
    只对startPointIndex=0且endPointIndex=0的字段进行验证
    """
    logger.info("--- 执行hitValue质检节点 ---")
    
    if not state.crf_result:
        logger.info("hitValue质检: 没有CRF结果，跳过质检")
        return state
    
    validation_count = 0
    corrected_count = 0
    
    # 遍历所有字段，找到溯源失败的
    for crf_table in state.crf_result:
        for crf_index in crf_table.crfIndexList:
            if crf_index.dataSource:
                for data_source in crf_index.dataSource:
                    # 检查是否需要质检：溯源失败但有hitValue
                    if (data_source.startPointIndex == 0 and 
                        data_source.endPointIndex == 0 and 
                        data_source.hitValue):
                        
                        validation_count += 1
                        logger.info(f"hitValue质检: 发现需要验证的字段 '{crf_index.crfIndexName}' = '{crf_index.crfIndexValue}'")
                        logger.info(f"hitValue质检: 原始hitValue = '{data_source.hitValue}'")
                        
                        # 对单个字段进行质检
                        was_corrected = validate_single_field(
                            crf_index.crfIndexName,
                            crf_index.crfIndexValue, 
                            data_source,
                            crf_index,
                            state
                        )
                        
                        if was_corrected:
                            corrected_count += 1
    
    logger.info(f"hitValue质检完成: 检查了{validation_count}个字段，修正了{corrected_count}个字段")
    return state


def validate_single_field(field_name: str, field_value: str, data_source: DataSource, crf_index, state: AssistantState) -> bool:
    """
    对单个字段进行LLM质检
    
    Args:
        field_name: 字段名
        field_value: 字段值
        data_source: 数据源对象
        crf_index: CRF索引对象，用于多片段时更新dataSource列表
        state: 状态对象
        
    Returns:
        bool: 是否进行了修正
    """
    try:
        # 获取原文内容
        source_text = get_source_text_for_validation(data_source, state)
        if not source_text:
            logger.warning(f"hitValue质检: 无法获取字段 '{field_name}' 的原文内容")
            return False
        
        # 构建质检提示词
        prompt = build_validation_prompt(field_name, field_value, data_source.hitValue, source_text)
        
        # 调用LLM进行验证
        logger.info(f"hitValue质检: 开始验证字段 '{field_name}'")
        
        # 获取provider和model参数，与icrc_nodes.py保持一致
        provider = os.getenv("LLM_PROVIDER", "openrouter")
        model = os.getenv("LLM_MODEL_NAME")
        temperature = float(os.getenv("LLM_TEMPERATURE", "0"))
        
        response, thinking = call_llm_api(
            prompt=prompt,
            provider=provider,
            model=model,
            temperature=temperature,
            is_form_extraction=True
        )
        
        # 解析LLM响应
        validation_result = parse_validation_response(response)
        
        if validation_result:
            logger.info(f"hitValue质检: 字段 '{field_name}' 验证结果 - exists: {validation_result.get('exists', True)}, confidence: {validation_result.get('confidence', 0)}")
            
            # 如果hitValue存在但位置索引错误，重新计算位置
            if (validation_result.get('exists', True) and 
                validation_result.get('confidence', 0) >= 0.8 and
                data_source.startPointIndex == 0 and 
                data_source.endPointIndex == 0):
                
                logger.info(f"hitValue质检: 字段 '{field_name}' hitValue内容正确，但位置索引需要修复")
                
                # 重新计算位置索引
                recalculate_position_index(data_source, state)
                
                # 检查位置是否修复成功
                if data_source.startPointIndex > 0 or data_source.endPointIndex > 0:
                    logger.info(f"hitValue质检: 字段 '{field_name}' 位置索引修复成功 - start: {data_source.startPointIndex}, end: {data_source.endPointIndex}")
                    return True
                else:
                    logger.warning(f"hitValue质检: 字段 '{field_name}' 位置索引修复失败，但内容验证通过")
                    return False
            
            # 如果置信度高且需要修正，创建多个DataSource
            elif (not validation_result.get('exists', True) and 
                validation_result.get('confidence', 0) >= 0.8 and
                validation_result.get('corrected_hitvalue')):
                
                original_hitvalue = data_source.hitValue
                corrected_hitvalue_raw = validation_result['corrected_hitvalue']
                
                # 处理数组格式的corrected_hitvalue
                if isinstance(corrected_hitvalue_raw, list) and len(corrected_hitvalue_raw) > 1:
                    # 多片段：创建多个DataSource
                    logger.info(f"hitValue质检: 字段 '{field_name}' 发现多个修正片段: {corrected_hitvalue_raw}")
                    
                    # 清空当前的dataSource列表
                    crf_index.dataSource = []
                    
                    # 为每个片段创建独立的DataSource
                    for i, segment in enumerate(corrected_hitvalue_raw):
                        if segment.strip():  # 跳过空片段
                            new_data_source = DataSource(
                                docId=data_source.docId,
                                recordId=data_source.recordId,
                                hitValue=segment.strip(),
                                tableNameDesc=data_source.tableNameDesc,
                                tableName=data_source.tableName,
                                columnName=data_source.columnName,
                                startPointIndex=0,  # 初始值，后续重新计算
                                endPointIndex=0
                            )
                            
                            # 重新计算这个片段的位置索引
                            recalculate_position_index(new_data_source, state)
                            
                            # 添加到dataSource列表
                            crf_index.dataSource.append(new_data_source)
                            
                            logger.info(f"hitValue质检: 创建片段 {i+1}/{len(corrected_hitvalue_raw)}: '{segment.strip()}'")
                    
                    logger.info(f"hitValue质检: 字段 '{field_name}' 已分解为 {len(crf_index.dataSource)} 个独立DataSource")
                    
                elif isinstance(corrected_hitvalue_raw, list) and len(corrected_hitvalue_raw) == 1:
                    # 单片段数组：直接更新现有DataSource
                    corrected_hitvalue = corrected_hitvalue_raw[0]
                    data_source.hitValue = corrected_hitvalue
                    logger.info(f"hitValue质检: 字段 '{field_name}' hitValue已修正")
                    logger.info(f"hitValue质检: 原始值 = '{original_hitvalue}'")
                    logger.info(f"hitValue质检: 修正值 = '{corrected_hitvalue}'")
                    
                    # 重新计算位置索引
                    recalculate_position_index(data_source, state)
                else:
                    # 字符串格式：保持向后兼容
                    corrected_hitvalue = corrected_hitvalue_raw
                    data_source.hitValue = corrected_hitvalue
                    logger.info(f"hitValue质检: 字段 '{field_name}' hitValue已修正")
                    logger.info(f"hitValue质检: 原始值 = '{original_hitvalue}'")
                    logger.info(f"hitValue质检: 修正值 = '{corrected_hitvalue}'")
                    
                    # 重新计算位置索引
                    recalculate_position_index(data_source, state)
                
                return True
        
        return False
        
    except Exception as e:
        logger.warning(f"hitValue质检: 字段 '{field_name}' 质检失败 - {str(e)}")
        # 质检失败不影响主流程
        return False


def get_source_text_for_validation(data_source: DataSource, state: AssistantState) -> str:
    """
    获取用于验证的原文内容
    
    Args:
        data_source: 数据源对象
        state: 状态对象
        
    Returns:
        str: 原文内容
    """
    try:
        # 根据docId和recordId找到对应的visit内容
        if not state.app_params or not state.app_params.metaInfo or not state.app_params.metaInfo.visits:
            return ""
        
        for visit in state.app_params.metaInfo.visits:
            for visit_data in visit.data:
                if visit_data.docId == data_source.docId:
                    for record in visit_data.records:
                        if record.recordId == data_source.recordId:
                            return record.content or ""
        
        logger.warning(f"未找到docId={data_source.docId}, recordId={data_source.recordId}的原文内容")
        return ""
        
    except Exception as e:
        logger.error(f"获取原文内容失败: {str(e)}")
        return ""


def load_validation_prompt_template() -> str:
    """
    加载验证提示词模板
    
    Returns:
        str: 提示词模板内容
    """
    try:
        with open(PROMPT_FILE_PATH, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.warning(f"加载提示词文件失败: {str(e)}")
        # 返回默认提示词
        return get_default_validation_prompt()


def get_default_validation_prompt() -> str:
    """
    获取默认的验证提示词（备用）
    
    Returns:
        str: 默认提示词模板
    """
    return """你需要验证医疗文档中的溯源信息是否准确。

字段名：{field_name}
提取值：{field_value}
溯源片段：{hit_value}

原文：
{source_text}

请验证溯源片段是否在原文中真实存在。如果不存在，请从原文中找到能支撑提取值的真实片段。

请用以下JSON格式回答：
{{
  "exists": true/false,
  "confidence": 0.0-1.0,
  "corrected_hitvalue": ["修正后的片段1", "修正后的片段2"] 或 null
}}

要求：
1. exists=true表示溯源片段在原文中存在，false表示不存在
2. confidence表示你的判断置信度，0.0-1.0之间
3. 如果exists=false且confidence>=0.8，请在corrected_hitvalue中提供修正的片段
4. 修正的片段必须在原文中真实存在，且能支撑提取值

请开始验证："""


def build_validation_prompt(field_name: str, field_value: str, hit_value: str, source_text: str) -> str:
    """
    构建验证提示词
    
    Args:
        field_name: 字段名
        field_value: 字段值
        hit_value: 溯源片段
        source_text: 原文内容
        
    Returns:
        str: 构建的提示词
    """
    # 限制原文长度，避免Token过多
    if len(source_text) > 3000:
        source_text = source_text[:3000] + "..."
    
    # 加载提示词模板
    template = load_validation_prompt_template()
    
    # 替换模板变量
    prompt = template.format(
        field_name=field_name,
        field_value=field_value,
        hit_value=hit_value,
        source_text=source_text
    )
    
    return prompt


def parse_validation_response(response: str) -> Optional[dict]:
    """
    解析LLM验证响应
    
    Args:
        response: LLM响应文本
        
    Returns:
        dict: 解析后的结果，如果解析失败返回None
    """
    try:
        # 使用现有的JSON解析器
        parser = DuplicateKeyJsonParser()
        
        # 尝试直接解析
        result = parser.parse(response)
        
        # 验证必需字段
        if isinstance(result, dict) and 'exists' in result and 'confidence' in result:
            return result
        
        # 如果直接解析失败，尝试提取JSON部分
        import re
        json_match = re.search(r'\{[^{}]*\}', response)
        if json_match:
            json_str = json_match.group()
            result = parser.parse(json_str)
            if isinstance(result, dict) and 'exists' in result and 'confidence' in result:
                return result
        
        logger.warning(f"解析验证响应失败，响应内容: {response[:200]}...")
        return None
        
    except Exception as e:
        logger.warning(f"解析验证响应异常: {str(e)}")
        return None


def recalculate_position_index(data_source: DataSource, state: AssistantState):
    """
    重新计算位置索引
    
    Args:
        data_source: 数据源对象
        state: 状态对象
    """
    try:
        # 获取原文内容
        source_text = get_source_text_for_validation(data_source, state)
        if not source_text or not data_source.hitValue:
            return
        
        # 使用现有的溯源方法重新计算位置
        corrected_text, start_idx, end_idx = get_modify_evidence(data_source.hitValue, source_text)
        
        if corrected_text and start_idx is not None and end_idx is not None:
            data_source.startPointIndex = start_idx
            data_source.endPointIndex = end_idx
            logger.info(f"hitValue质检: 重新计算位置索引 - start: {start_idx}, end: {end_idx}")
        else:
            logger.warning(f"hitValue质检: 无法重新计算位置索引，保持原值")
            
    except Exception as e:
        logger.warning(f"重新计算位置索引失败: {str(e)}")