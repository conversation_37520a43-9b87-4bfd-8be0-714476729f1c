from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from app.domain.schemas import PvAssistantAppParams
from app.utils.stream_llm import call_llm_api_stream  # 新增导入
import json
import json_repair
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

model_mapping = {
    "openrouter": {
        "deepseek-r1": "deepseek/deepseek-r1",
        "qwq-32b": "qwen/qwq-32b",
        "qwen3-32b": "qwen/qwen3-32b"
    },
    "dashscope": {
        "deepseek-r1": "deepseek-r1",
        "qwq-32b": "qwq-32b",
        "qwen3-32b": "qwen3-32b"
    }
}

def get_model_by_provider(provider, model):
    if provider in model_mapping:
        if model in model_mapping[provider]:
            return model_mapping[provider][model]
    return model

def pv_extract_form_data_stream(app_params: PvAssistantAppParams, llm: ChatOpenAI):  # llm is now required
    """提示词前端传入，流式返回"""
    logger.info("def pv_extract_form_data_stream [stream mode]")
    if not app_params:
        logger.warning("PV Extractor: 'app_params' missing in state.")
        yield {"content": None, "thinking_process": None, "error": "Request state is missing required parameters."}
        return
    
    prompt = app_params.prompt
    if not prompt:
        logger.warning("PV Extractor: 'app_params.prompt' missing in state.")
        yield {"content": None, "thinking_process": None, "error": "Request is missing prompt."}
        return

    questions = app_params.questions
    knowledge_context = ""
    for q in questions:
        knowledge_context += q.content if q.content.startswith("附上用户上传的知识") else ""

    # Load the base template from the external .md file
    try:
        prompt_file_path = Path(__file__).parent / "pv_extract_form_data_stream.md"
        base_prompt_template = prompt_file_path.read_text(encoding='utf-8')
    except FileNotFoundError:
        logger.error("PV Extractor: Prompt template file 'pv_extract_form_data_stream.md' not found.")
        yield {"content": None, "thinking_process": None, "error": "Internal error: Prompt template file missing."}
        return
    except Exception as e:
        logger.error(f"PV Extractor: Error reading prompt template file 'pv_extract_form_data_stream.md': {e}")
        yield {"content": None, "thinking_process": None, "error": "Internal error: Could not read prompt template."}
        return

    prompt_template = PromptTemplate(
        template=base_prompt_template,
        input_variables=["query", "source_text"],
        template_format="jinja2"
    )

    prompt_input = {
        "prompt": prompt,
        "source_text": knowledge_context
    }
    prompt_value = prompt_template.invoke(prompt_input)
    prompt_text = prompt_value.to_string()
    logger.info(f"Rendered prompt text: \n{prompt_text[:800]}")

    # default to deepseek-r1 provided by dashscope
    provider = app_params.provider if app_params.provider else "dashscope"
    model = app_params.model if app_params.model else "qwen-long"
    model = get_model_by_provider(provider, model)
    temperature = 0

    think_logic = ""
    content = ""
    event_processing_yield = False
    # 流式调用LLM
    for chunk in call_llm_api_stream(
        prompt=prompt_text,
        provider=provider,
        model=model,
        temperature=temperature
    ):
        # chunk: {"content": ..., "thinking_process": ...}
        # logger.info(f"chunk in pv nodes: {chunk}")
        if chunk["thinkLogic"]:
            think_logic += chunk["thinkLogic"]
            result = {
                "event": "thinking",
                "content": chunk["thinkLogic"]
            }
            yield result
        elif chunk["content"]:
            content += chunk["content"]
            if not event_processing_yield:
                event_processing_yield = True
                result = {
                    "event": "processing"
                }
                yield result
    logger.info(f"thinkLogic: {think_logic} \ncontent: {content}")
    try:
        result_json = normalize_result_json(content)
        result = {
            "event": "complete",
            "data": {
                "resultJson": result_json
            }
        }
        yield result
    except Exception as e:
        result = {
            "event": "error",
            "content": content
        }
        yield result


def clean_json(content):
    if "```json" in content:
        content = content.split("```json")[1].split("```")[0].strip()
    content = json_repair.repair_json(content)
    return json.loads(content)

def normalize_result_json(data):
    data = clean_json(data)
    normalized = []
    # 情况1：输入是数组
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict) and "selector" in item and "value" in item:
                normalized.append({
                    "selector": item["selector"],
                    "value": item.get("value", ""),
                    "type": item.get("type", "")
                })
    
    # 情况2：输入是动态键的对象
    elif isinstance(data, dict) and "selector" not in data:
        for selector, content in data.items():
            if isinstance(content, dict):
                normalized.append({
                    "selector": selector,
                    "value": content.get("value", ""),
                    "type": content.get("type", "")
                })
    
    # 情况3：输入是单对象（直接包含 selector 和 value）
    elif isinstance(data, dict) and "selector" in data and "value" in data:
        normalized.append({
            "selector": data["selector"],
            "value": data.get("value", ""),
            "type": data.get("type", "")
        })
    
    return normalized if normalized else data