"""
意图解析节点 - 负责解析用户查询的意图和提取关键信息
"""
import json
import logging
from typing import Dict, Any

from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from app.domain.schemas import AssistantState

# 配置日志
logger = logging.getLogger(__name__)

# 定义提示模板
INTENT_PROMPT = """你是一个智能助手，负责识别用户查询的意图和提取关键信息。

用户的查询是: {query}

请分析这个查询，并确定用户的主要意图。可能的意图类别包括：
1. district_query - 查询某个地区的行政区划信息（如城市的区县划分、行政级别等）
2. weather_query - 查询某个地区的天气信息（如温度、降水、天气状况等）
3. time_query - 查询当前时间或特定地区的时间
4. user_query - 查询用户信息（如账户信息、个人资料等）
5. unknown - 其他不属于以上四种的意图

请以JSON格式返回以下信息：
```json
{{
  "intent": "识别后的意图：英文",
  "location": "提取的地点名称，如果查询中包含地点，否则为null",
  "user_name": "提取的用户名称，如果查询中包含用户名，否则为null"
}}
```

只返回JSON，不要有其他前缀或解释。
"""

def parse_intent(state: AssistantState, llm: ChatOpenAI) -> AssistantState:
    """
    解析用户意图和提取位置信息
    
    Args:
        state: 当前助手状态
        llm: 语言模型实例
        
    Returns:
        更新后的助手状态
    """
    query = state.query

    # 使用LLM识别意图
    prompt = ChatPromptTemplate.from_template(INTENT_PROMPT)
    chain = prompt | llm
    result = chain.invoke({"query": query})

    # 解析结果
    try:
        content = result.content
        # 提取JSON部分
        if "```json" in content:
            content = content.split("```json")[1].split("```")[0].strip()

        parsed = json.loads(content)

        # 更新状态
        state.intent = parsed.get("intent")
        state.location = parsed.get("location")
        state.user_name = parsed.get("user_name")

        logger.info(f"识别到意图: {state.intent}, 位置: {state.location}, 用户名: {state.user_name}")

    except Exception as e:
        logger.error(f"解析意图时出错: {str(e)}")
        state.intent = "unknown"
        state.location = None
        state.error_message = f"Failed to parse intent: {e}"

    return state
