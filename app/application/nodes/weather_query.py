"""
天气查询节点 - 负责查询天气信息
"""
import logging
from typing import Dict, Any

from langchain_core.tools import BaseTool
from app.domain.schemas import AssistantState, AmapWeatherResponse, BaseResponse

# 配置日志
logger = logging.getLogger(__name__)

def query_weather(state: AssistantState, tools: list[BaseTool]) -> Dict[str, Any]:
    """
    查询天气信息
    
    Args:
        state: 当前助手状态
        tools: LangChain 工具列表
        
    Returns:
        包含更新状态的字典
    """
    adcode = state.adcode
    if not adcode:
        error_msg = "缺少 adcode，无法查询天气"
        logger.warning(error_msg)
        return {"error_message": error_msg, "weather_result": {"code": 400, "message": error_msg, "data": None}}

    weather_tool = next((tool for tool in tools if tool.name == "query_amap_weather"), None)
    if not weather_tool:
        error_msg = "天气查询工具不可用"
        logger.error(error_msg)
        return {"error_message": error_msg, "weather_result": {"code": 500, "message": error_msg, "data": None}}

    updates: Dict[str, Any] = {}
    try:
        result = weather_tool.invoke({"city": adcode})

        # 添加详细的调试日志
        logger.info(f"天气查询原始响应类型: {type(result)}")

        # 将响应转换为字典格式
        if hasattr(result, "model_dump"):
            # Pydantic v2
            result_dict = result.model_dump()
            logger.debug(f"使用 model_dump() 转换响应: {result_dict}")
        elif hasattr(result, "dict"):
            # Pydantic v1
            result_dict = result.dict()
            logger.debug(f"使用 dict() 转换响应: {result_dict}")
        else:
            # 如果不是 Pydantic 模型，尝试直接使用
            if isinstance(result, dict):
                result_dict = result
            else:
                # 非字典且非 Pydantic 模型的响应视为错误
                logger.error(f"天气查询返回了无法处理的响应类型: {type(result)}")
                updates['error_message'] = "天气查询返回了无法处理的响应"
                return updates

        # 记录转换后的响应
        logger.info(f"天气查询响应内容: {result_dict}")

        # 处理成功响应 - 转换并标准化
        if result_dict.get('status') == '1':
            # API 调用成功
            lives_data = result_dict.get('lives')
            if lives_data and isinstance(lives_data, list):
                # 创建嵌套数据
                nested_data = {'lives': lives_data}
                standardized_result = {
                    'code': 0,
                    'message': 'OK',
                    'data': nested_data
                }
                logger.debug(f"标准化天气响应成功: {standardized_result}")
            else:
                # API 成功，但缺少 lives 数据
                logger.warning(f"API 成功但缺少有效的 lives 数据: {result_dict}")
                standardized_result = {
                    'code': 0,
                    'message': 'OK - No live weather data available',
                    'data': None
                }
                # 可以在这里设置 error_message，但 generate_response 也会处理 data 为 None 的情况
        else:
            # API 调用失败
            error_code = int(result_dict.get('infocode', 99999))
            error_message = result_dict.get('info', 'Unknown API Error')
            logger.warning(f"天气查询 API 失败 ({error_code}): {error_message}")
            standardized_result = {
                'code': error_code,
                'message': error_message,
                'data': None
            }
            updates['error_message'] = f"天气 API 错误 ({error_code}): {error_message}"

        # 存储标准化后的响应数据
        updates['weather_result'] = standardized_result

    except Exception as e:
        error_msg = f"查询天气时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        # Overwrite updates dictionary on exception
        updates = {
            "error_message": error_msg,
            "weather_result": {"code": 500, "message": error_msg, "data": None}
        }

    return updates
