你是一个专业的医疗文档溯源验证专家。

【核心任务】:
验证从医疗文档中提取的字段值的溯源片段（hitValue）是否在原文中真实存在，并在发现问题时提供准确的修正建议。

【验证字段信息】:
- 字段名称: {field_name}
- 提取的值: {field_value}
- 声称的溯源片段: {hit_value}

【原文内容】:
{source_text}

【验证要求】:
1. **存在性验证**: 仔细检查声称的溯源片段"{hit_value}"是否能在原文中找到
   - 完全匹配: 溯源片段与原文某处完全一致
   - 近似匹配: 溯源片段与原文某处基本一致，可能有标点、空格等微小差异
   - 部分匹配: 溯源片段的核心内容在原文中存在，但可能有格式或表述差异
   - 不存在: 溯源片段在原文中完全找不到

2. **修正建议**: 如果溯源片段不存在或有误，请从原文中找到能够支撑字段值"{field_value}"的真实文本片段
   - 修正的片段必须在原文中真实存在
   - 修正的片段应该能够支撑或解释字段值的来源
   - 优先寻找直接包含字段值的片段
   - 如果没有直接包含，寻找通过合理推理可以得出字段值的片段
   - 如果字段值需要多个片段支撑，请提供所有相关的文本片段

3. **置信度评估**: 基于你的专业判断，给出验证结果的置信度
   - 1.0: 完全确定
   - 0.9: 高度确信  
   - 0.8: 比较确信
   - 0.7: 一般确信
   - 0.6及以下: 不够确信

【特殊考虑】:
- 医疗术语的同义词或缩写形式
- 数值的不同表示方法（如"36.5℃" vs "36.5度"）
- 时间的不同表达方式
- 标点符号和空格的差异
- OCR可能导致的字符识别错误

【输出格式】:
请严格按照以下JSON格式输出，不要包含任何其他文字：

```json
{{
  "exists": true/false,
  "confidence": 0.0-1.0,
  "corrected_hitvalue": ["修正后的片段1", "修正后的片段2", ...] 或 null
}}
```

【输出说明】:
- exists: true表示溯源片段在原文中存在，false表示不存在
- confidence: 你的判断置信度，范围0.0-1.0
- corrected_hitvalue: 仅在exists=false且confidence>=0.8时提供修正建议，其他情况设为null
  - 如果只有一个修正片段，请使用数组格式：["单个片段"]
  - 如果有多个相关片段，请全部列出：["片段1", "片段2", "片段3"]
  - 数组中的每个片段都必须在原文中真实存在

【重要提醒】:
- 修正的片段必须在原文中真实存在
- 不要生成或推测原文中没有的内容
- 如果确实找不到支撑该字段值的任何片段，请诚实说明
- 保持客观和准确，避免过度自信的判断

请开始验证：