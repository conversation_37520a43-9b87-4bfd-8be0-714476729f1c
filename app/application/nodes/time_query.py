"""
时间查询节点 - 负责查询当前时间
"""
import logging
from typing import Dict, Any

from app.domain.schemas import AssistantState
from app.core.executor import GenericToolExecutor

# 配置日志
logger = logging.getLogger(__name__)

def run_get_current_time(state: AssistantState, executor: GenericToolExecutor) -> Dict[str, Any]:
    """
    查询当前时间
    
    Args:
        state: 当前助手状态
        executor: 工具执行器实例
        
    Returns:
        包含更新状态的字典
    """
    logger.info("Executing run_get_current_time node")
    try:
        # 使用 executor 调用时间工具
        tool_result = executor.execute("get_current_time", {})
        logger.debug(f"Raw get_current_time result: {tool_result}")

        # 提取时间字符串
        time_str = None
        # 检查执行是否成功 (code 0)
        if tool_result.get("code") == 0:
            # 检查 data 字段是否为时间字符串
            if isinstance(tool_result.get("data"), str):
                time_str = tool_result["data"]
            # 检查 data 是否为包含时间字符串的字典
            elif isinstance(tool_result.get("data"), dict):
                time_str = tool_result["data"].get("current_time")

        if time_str:
            logger.info(f"Successfully got current time: {time_str}")
            return {"time_result": time_str}
        else:
            logger.error(f"Failed to extract time from tool result: {tool_result}")
            return {"error_message": "Failed to execute or parse get_current_time tool."}
    except Exception as e:
        logger.error(f"Error executing get_current_time tool: {e}", exc_info=True)
        return {"error_message": f"Error calling time tool: {e}"}
