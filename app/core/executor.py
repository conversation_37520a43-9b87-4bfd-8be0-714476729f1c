"""
工具执行器模块 - 负责加载工具配置并执行工具调用
"""
import os
import json
import logging
from typing import Dict, Any, List, Optional
import jsonschema
from pydantic import ValidationError

from app.core.connections import ConnectionManager
from app.core.connectors.factory import ConnectorFactory
from app.infrastructure.langchain_tools import create_dynamic_args_schema

logger = logging.getLogger(__name__)


class GenericToolExecutor:
    """通用工具执行器 - 负责加载工具配置并执行工具调用"""
    
    def __init__(self, config_path: str):
        """
        初始化工具执行器
        
        Args:
            config_path: 工具配置文件路径
        """
        self.config_path = config_path
        self.tools: Dict[str, Dict[str, Any]] = {}
        self.connection_manager = None
        self.connector_factory = None
        
        self._load_config()
        self._initialize_connectors()
        
    def _load_config(self) -> None:
        """加载工具配置文件"""
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"配置文件不存在: {self.config_path}")
                return
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 初始化连接管理器
            connections = config.get("connections", [])
            self.connection_manager = ConnectionManager(connections)
            
            # 加载工具
            tools = config.get("tools", [])
            for tool in tools:
                tool_name = tool.get("name")
                if not tool_name:
                    logger.warning("发现无名称的工具配置，已跳过")
                    continue
                    
                self.tools[tool_name] = tool
                logger.info(f"已加载工具: {tool_name}")
                
        except json.JSONDecodeError as e:
            logger.error(f"解析配置文件时发生错误: {str(e)}")
        except Exception as e:
            logger.error(f"加载配置时发生错误: {str(e)}")
            
    def _initialize_connectors(self) -> None:
        """初始化连接器工厂"""
        self.connector_factory = ConnectorFactory(self.connection_manager)
            
    def get_tool_names(self) -> List[str]:
        """获取所有可用工具名称"""
        return list(self.tools.keys())
        
    def get_tool_config(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """获取指定工具的配置"""
        return self.tools.get(tool_name)
        
    def validate_arguments(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        验证工具调用参数
        
        Args:
            tool_name: 工具名称
            arguments: 调用参数
            
        Returns:
            None表示验证通过，否则返回错误信息
        """
        tool_config = self.get_tool_config(tool_name)
        if not tool_config:
            return {"code": 404, "message": f"工具不存在: {tool_name}", "data": None}

        try:
            # 使用 create_dynamic_args_schema 获取基于 OpenAPI 的模型
            args_schema_model = create_dynamic_args_schema(tool_name, tool_config)
            # 使用模型验证参数
            args_schema_model(**arguments)
            return None # 验证通过
        except ValidationError as e:
            logger.error(f"工具 '{tool_name}' 参数验证失败: {e}")
            # 格式化 Pydantic 错误信息
            error_details = [f"{err['loc'][0]}: {err['msg']}" for err in e.errors()]
            return {
                "code": 422, # Unprocessable Entity
                "message": f"参数验证失败: {'; '.join(error_details)}",
                "data": None
            }
        except Exception as e:
            # 处理创建 schema 或其他意外错误
            logger.error(f"验证工具 '{tool_name}' 参数时发生意外错误: {e}")
            return {
                "code": 500,
                "message": f"参数验证时发生内部错误: {str(e)}",
                "data": None
            }

    def execute(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行工具调用
        
        Args:
            tool_name: 工具名称
            arguments: 调用参数
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        # 检查工具是否存在
        tool_config = self.get_tool_config(tool_name)
        if not tool_config:
            return self._error_response(404, f"工具不存在: {tool_name}")
            
        # 验证参数
        validation_error = self.validate_arguments(tool_name, arguments)
        if validation_error:
            return validation_error
            
        # 根据工具类型选择连接器
        tool_type = tool_config.get("type")
        
        try:
            # 使用连接器工厂获取适合的连接器
            connector = self.connector_factory.get_connector_for_tool(tool_config)
            if connector:
                result = connector.execute(tool_config, arguments)
            else:
                return self._error_response(400, f"不支持的工具类型: {tool_type}")
                
            # 验证返回值
            # TODO: 根据工具的returns schema验证结果
            
            return result
            
        except Exception as e:
            logger.error(f"执行工具时发生错误: {str(e)}")
            return self._error_response(500, f"执行工具时发生错误: {str(e)}")
            
    def _error_response(self, code: int, message: str) -> Dict[str, Any]:
        """创建标准错误响应"""
        logger.error(message)
        return {
            "code": code,
            "message": message,
            "data": None
        }
