"""
基础连接器接口 - 定义所有连接器必须实现的接口
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BaseConnector(ABC):
    """
    连接器基类 - 所有连接器必须继承此类并实现其方法
    """
    
    @abstractmethod
    def execute(self, tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行工具调用
        
        Args:
            tool_config: 工具配置
            arguments: 调用参数
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        pass
    
    def _error_response(self, code: int, message: str) -> Dict[str, Any]:
        """
        创建标准错误响应
        
        Args:
            code: 错误码
            message: 错误信息
            
        Returns:
            标准化的错误响应字典
        """
        logger.error(message)
        return {
            "code": code,
            "message": message,
            "data": None
        }
    
    def _success_response(self, data: Any, message: str = "OK") -> Dict[str, Any]:
        """
        创建标准成功响应
        
        Args:
            data: 响应数据
            message: 成功信息
            
        Returns:
            标准化的成功响应字典
        """
        return {
            "code": 0,
            "message": message,
            "data": data
        }
