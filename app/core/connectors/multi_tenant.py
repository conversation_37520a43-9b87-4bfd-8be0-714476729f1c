"""
多租户连接器 - 支持根据租户信息选择不同的API端点和认证信息
"""
import logging
from typing import Dict, Any, Optional

from app.core.connectors.http import HttpConnector

logger = logging.getLogger(__name__)

class MultiTenantConnector(HttpConnector):
    """多租户连接器 - 支持根据租户信息选择不同的API端点和认证信息"""
    
    def execute(self, tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行多租户API调用
        
        Args:
            tool_config: 工具配置
            arguments: 调用参数
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        try:
            # 从参数中提取租户ID
            tenant_id = arguments.pop("tenant_id", None)
            if not tenant_id:
                return self._error_response(400, "缺少租户ID (tenant_id)")
                
            # 根据租户ID获取租户特定的配置
            tenant_config = self._get_tenant_config(tenant_id)
            if not tenant_config:
                return self._error_response(404, f"找不到租户配置: {tenant_id}")
                
            # 修改工具配置以使用租户特定的API端点和认证信息
            modified_tool_config = self._apply_tenant_config(tool_config, tenant_config)
            
            # 使用修改后的配置执行API调用
            return super().execute(modified_tool_config, arguments)
            
        except Exception as e:
            logger.error(f"执行多租户API调用时发生错误: {str(e)}")
            return self._error_response(500, f"执行多租户API调用时发生错误: {str(e)}")
    
    def _get_tenant_config(self, tenant_id: str) -> Optional[Dict[str, Any]]:
        """
        获取租户特定的配置
        
        Args:
            tenant_id: 租户ID
            
        Returns:
            租户配置字典，如果找不到则返回None
        """
        # 从连接管理器获取租户配置
        # 这里假设连接管理器有一个get_tenant_config方法
        if hasattr(self.connection_manager, 'get_tenant_config'):
            return self.connection_manager.get_tenant_config(tenant_id)
            
        # 如果连接管理器没有提供租户配置方法，记录警告
        logger.warning(f"连接管理器未提供租户配置方法，无法获取租户 {tenant_id} 的配置")
        return None
    
    def _apply_tenant_config(self, tool_config: Dict[str, Any], tenant_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        将租户配置应用到工具配置
        
        Args:
            tool_config: 原始工具配置
            tenant_config: 租户特定配置
            
        Returns:
            修改后的工具配置
        """
        # 创建工具配置的深拷贝，避免修改原始配置
        import copy
        modified_config = copy.deepcopy(tool_config)
        
        # 获取OpenAPI规范
        openapi_spec = modified_config.get("openapi")
        if not openapi_spec:
            logger.warning("工具配置缺少openapi字段，无法应用租户配置")
            return modified_config
            
        # 修改服务器URL
        if "api_base_url" in tenant_config and "servers" in openapi_spec:
            openapi_spec["servers"][0]["url"] = tenant_config["api_base_url"]
            
        # 修改连接ID (用于API认证)
        if "connection_id" in tenant_config:
            modified_config["connection_id"] = tenant_config["connection_id"]
            
        # 可以根据需要添加更多的租户特定配置
            
        return modified_config
