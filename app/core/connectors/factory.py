"""
连接器工厂模块 - 负责创建和管理不同类型的连接器实例
"""
import logging
from typing import Dict, Type, Optional

from app.core.connectors.base import BaseConnector
from app.core.connectors.http import HttpConnector
from app.core.connectors.amap import AmapConnector
from app.core.connectors.local_function import LocalFunctionConnector
from app.core.connectors.multi_tenant import MultiTenantConnector

logger = logging.getLogger(__name__)

class ConnectorFactory:
    """连接器工厂 - 负责创建和管理不同类型的连接器实例"""
    
    def __init__(self, connection_manager):
        """
        初始化连接器工厂
        
        Args:
            connection_manager: 连接管理器实例
        """
        self.connection_manager = connection_manager
        self._connector_registry: Dict[str, Type[BaseConnector]] = {}
        self._connector_instances: Dict[str, BaseConnector] = {}
        
        # 注册默认连接器
        self.register_connector("http", HttpConnector)
        self.register_connector("amap", AmapConnector)
        self.register_connector("function", LocalFunctionConnector)
        self.register_connector("multi_tenant", MultiTenantConnector)
    
    def register_connector(self, connector_type: str, connector_class: Type[BaseConnector]) -> None:
        """
        注册连接器类型
        
        Args:
            connector_type: 连接器类型标识
            connector_class: 连接器类
        """
        self._connector_registry[connector_type] = connector_class
        logger.info(f"已注册连接器类型: {connector_type} -> {connector_class.__name__}")
    
    def get_connector(self, connector_type: str) -> Optional[BaseConnector]:
        """
        获取连接器实例
        
        Args:
            connector_type: 连接器类型标识
            
        Returns:
            连接器实例，如果类型不存在则返回None
        """
        # 检查是否已有实例
        if connector_type in self._connector_instances:
            return self._connector_instances[connector_type]
        
        # 检查是否已注册类型
        if connector_type not in self._connector_registry:
            logger.error(f"未注册的连接器类型: {connector_type}")
            return None
        
        # 创建新实例
        connector_class = self._connector_registry[connector_type]
        try:
            # 根据连接器类型决定是否传入connection_manager
            if connector_type in ["http", "amap"]:
                instance = connector_class(self.connection_manager)
            else:
                instance = connector_class()
                
            self._connector_instances[connector_type] = instance
            logger.info(f"已创建连接器实例: {connector_type}")
            return instance
            
        except Exception as e:
            logger.error(f"创建连接器实例时发生错误: {connector_type} - {str(e)}")
            return None
    
    def get_connector_for_tool(self, tool_config: Dict) -> Optional[BaseConnector]:
        """
        根据工具配置获取适合的连接器实例
        
        Args:
            tool_config: 工具配置
            
        Returns:
            连接器实例，如果无法确定类型则返回None
        """
        tool_type = tool_config.get("type")
        
        if tool_type == "rest":
            # 检查是否是多租户工具
            is_multi_tenant = tool_config.get("multi_tenant", False)
            if is_multi_tenant:
                return self.get_connector("multi_tenant")
                
            # 根据提供者选择不同的连接器
            provider = tool_config.get("provider", "")
            if provider == "amap":
                return self.get_connector("amap")
            else:
                return self.get_connector("http")
        elif tool_type == "function":
            return self.get_connector("function")
        else:
            logger.error(f"不支持的工具类型: {tool_type}")
            return None
