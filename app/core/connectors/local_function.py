"""
本地函数连接器 - 负责执行本地Python函数
"""
import logging
import importlib
from typing import Dict, Any

from app.core.connectors.base import BaseConnector

logger = logging.getLogger(__name__)

class LocalFunctionConnector(BaseConnector):
    """本地函数连接器 - 负责执行本地Python函数"""
    
    def execute(self, tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行本地函数调用
        
        Args:
            tool_config: 工具配置
            arguments: 调用参数
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        try:
            execution_details = tool_config.get("execution_details", {})
            module_path = execution_details.get("module_path") 
            function_name = execution_details.get("function_name")
            
            if not all([module_path, function_name]):
                return self._error_response(400, "工具配置不完整: 缺少module_path或function_name")
            
            # 动态导入模块和函数
            module = importlib.import_module(module_path)
            function = getattr(module, function_name)
            
            # 执行函数
            result = function(**arguments)
            
            # 如果函数已返回标准格式，直接返回
            if isinstance(result, dict) and all(k in result for k in ["code", "message"]):
                return result
                
            # 否则包装结果
            return self._success_response(result)
            
        except ImportError as e:
            return self._error_response(500, f"模块导入错误: {str(e)}")
        except AttributeError as e:
            return self._error_response(500, f"函数不存在: {str(e)}")
        except Exception as e:
            return self._error_response(500, f"执行函数时发生错误: {str(e)}")
