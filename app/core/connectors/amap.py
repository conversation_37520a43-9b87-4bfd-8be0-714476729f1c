"""
高德地图API连接器 - 专门处理高德地图API的响应格式
"""
import logging
from typing import Dict, Any

from app.core.connectors.http import HttpConnector

logger = logging.getLogger(__name__)

class AmapConnector(HttpConnector):
    """高德地图API连接器 - 专门处理高德地图API的响应格式"""
    
    def _process_response(self, response_data: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理高德API响应
        
        Args:
            response_data: API响应数据
            tool_config: 工具配置
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        # 高德API特殊处理 (基于其返回结构)
        if "status" in response_data:
            if response_data["status"] == "1":
                # 成功
                tool_name = tool_config.get("name", "")
                return self._success_response(
                    self._extract_data(response_data, tool_name),
                    response_data.get("info", "OK")
                )
            else:
                # 失败
                return {
                    "code": int(response_data.get("infocode", 1)),
                    "message": response_data.get("info", "API错误"),
                    "data": None
                }
        
        # 如果不是标准的高德API响应，使用默认处理
        return super()._process_response(response_data, tool_config)
    
    def _extract_data(self, response_data: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """
        从高德API响应中提取相关数据
        
        Args:
            response_data: API响应数据
            tool_name: 工具名称
            
        Returns:
            提取的数据字典
        """
        logger.debug(f"处理高德API响应，工具名称: {tool_name}")
        logger.debug(f"原始响应数据: {response_data}")
        
        # 根据工具名称提取相关字段
        if tool_name == "query_amap_district":
            extracted_data = {
                "districts": response_data.get("districts", []),
                "count": response_data.get("count"),
                "suggestion": response_data.get("suggestion", {})
            }
            logger.debug(f"提取的区划数据: {extracted_data}")
            return extracted_data
            
        elif tool_name == "query_amap_weather":
            lives = response_data.get("lives", [])
            forecasts = response_data.get("forecasts", [])
            
            # 检查是否有天气数据
            if not lives and not forecasts:
                logger.warning(f"高德天气API响应中未找到天气数据: {response_data}")
                return response_data  # 返回原始响应
                
            extracted_data = {
                "lives": lives,
                "forecasts": forecasts
            }
            logger.debug(f"提取的天气数据: {extracted_data}")
            return extracted_data
        
        # 默认返回整个响应
        logger.debug(f"使用默认提取逻辑，返回原始响应")
        return response_data
