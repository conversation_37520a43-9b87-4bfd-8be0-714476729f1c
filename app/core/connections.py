"""
连接管理模块 - 负责管理API连接和凭证
支持:
1. API Key 鉴权
2. 多租户配置管理
"""
import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ConnectionManager:
    """连接管理器 - 负责管理和提供API连接凭证"""
    
    def __init__(self, connections_config: list):
        """
        初始化连接管理器
        
        Args:
            connections_config: 从tools_config.json加载的connections配置列表
        """
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.tenant_configs: Dict[str, Dict[str, Any]] = {}
        self._load_connections(connections_config)
    
    def _load_connections(self, connections_config: list) -> None:
        """
        加载连接配置
        
        Args:
            connections_config: 连接配置列表
        """
        for connection in connections_config:
            connection_id = connection.get("id")
            if not connection_id:
                logger.warning("发现无ID的连接配置，已跳过")
                continue
                
            self.connections[connection_id] = connection
            logger.info(f"已加载连接配置: {connection_id}")
    
    def get_connection(self, connection_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的连接配置
        
        Args:
            connection_id: 连接ID
            
        Returns:
            连接配置字典或None（如果未找到）
        """
        return self.connections.get(connection_id)
    
    def get_api_key(self, connection_id: str) -> Optional[str]:
        """
        获取API Key类型连接的密钥值
        
        Args:
            connection_id: 连接ID
            
        Returns:
            API Key值或None（如果未找到或类型不匹配）
        """
        connection = self.get_connection(connection_id)
        if not connection:
            logger.error(f"未找到连接配置: {connection_id}")
            return None
            
        if connection.get("type") != "api_key":
            logger.error(f"连接类型不是api_key: {connection_id}")
            return None
            
        credentials = connection.get("credentials", {})
        env_var_name = credentials.get("api_key_env_var")
        
        if not env_var_name:
            logger.error(f"连接配置中未指定api_key_env_var: {connection_id}")
            return None
            
        api_key = os.environ.get(env_var_name)
        if not api_key:
            logger.error(f"环境变量未设置: {env_var_name}")
            return None
            
        return api_key
        
    def register_tenant_config(self, tenant_id: str, config: Dict[str, Any]) -> None:
        """
        注册租户配置
        
        Args:
            tenant_id: 租户ID
            config: 租户配置字典
        """
        self.tenant_configs[tenant_id] = config
        logger.info(f"已注册租户配置: {tenant_id}")
    
    def get_tenant_config(self, tenant_id: str) -> Optional[Dict[str, Any]]:
        """
        获取租户配置
        
        Args:
            tenant_id: 租户ID
            
        Returns:
            租户配置字典或None（如果未找到）
        """
        config = self.tenant_configs.get(tenant_id)
        if not config:
            logger.warning(f"未找到租户配置: {tenant_id}")
            return None
        return config
    
    def load_tenant_configs_from_file(self, file_path: str) -> bool:
        """
        从文件加载租户配置
        
        Args:
            file_path: 租户配置文件路径
            
        Returns:
            加载是否成功
        """
        import json
        try:
            if not os.path.exists(file_path):
                logger.error(f"租户配置文件不存在: {file_path}")
                return False
                
            with open(file_path, 'r', encoding='utf-8') as f:
                tenant_configs = json.load(f)
                
            for tenant_id, config in tenant_configs.items():
                self.register_tenant_config(tenant_id, config)
                
            logger.info(f"已从文件加载租户配置: {file_path}")
            return True
            
        except json.JSONDecodeError as e:
            logger.error(f"解析租户配置文件时发生错误: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"加载租户配置时发生错误: {str(e)}")
            return False
