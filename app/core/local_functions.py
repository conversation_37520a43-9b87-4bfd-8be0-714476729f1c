import datetime
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# 模拟的用户数据库
MOCK_USER_DB = {
    "test_user": {"gender": "未知", "age": 99, "origin": "测试系统"},
    "张三": {"gender": "男", "age": 25, "origin": "北京"},
    "李四": {"gender": "女", "age": 30, "origin": "上海"},
    "王五": {"gender": "男", "age": 22, "origin": "广州"},
}

def query_user_info(name: str) -> Dict[str, Any]:
    """模拟查询用户信息 (本地函数)"""
    logger.info(f"Executing query_user_info with name: {name}")
    user = MOCK_USER_DB.get(name)
    if user:
        logger.info(f"Found user: {name}")
        # Return success with code 0 and data
        return user
    else:
        logger.warning(f"User not found: {name}")
        # Return failure with specific code and message, data is None
        return {
            "code": 404,
            "message": f"用户 '{name}' 未找到",
            "data": None
        }

def get_current_time_func(**kwargs) -> str:
    """获取当前日期和时间。
# app/main.py line 25 approx
try:
    executor = GenericToolExecutor() # <<<--- Problem is here
    # ...
    Args:
        **kwargs: 接受任意关键字参数，但当前函数不使用它们。

    Returns:
        格式化后的当前日期和时间字符串 (YYYY-MM-DD HH:MM:SS)。
    """
    now = datetime.datetime.now()
    formatted_time = now.strftime("%Y年%m月%d日 %H时%M分%S秒")
    logger.info(f"Local function 'get_current_time_func' executed, returning: {formatted_time}")
    return formatted_time

# ==============================================================================
# 可以继续在此处添加其他本地函数
# ==============================================================================
