"""
连接器模块 - 负责执行不同类型的工具调用
"""
import logging
import importlib
from typing import Dict, Any, Optional, Callable, Union
import requests
from urllib.parse import urljoin
from app.domain.schemas import BaseResponse

logger = logging.getLogger(__name__)


class HttpConnector:
    """HTTP连接器 - 负责执行REST API调用 (基于OpenAPI配置)"""
    
    def __init__(self, connection_manager):
        """
        初始化HTTP连接器
        
        Args:
            connection_manager: 连接管理器实例
        """
        self.connection_manager = connection_manager
        
    def execute(self, tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行HTTP API调用
        
        Args:
            tool_config: 工具配置 (包含 openapi 字段)
            arguments: 调用参数
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        try:
            openapi_spec = tool_config.get("openapi")
            if not openapi_spec:
                return self._error_response(400, "工具配置缺少 openapi 字段")

            # 1. 解析 OpenAPI 配置
            server_url = openapi_spec.get("servers", [{}])[0].get("url")
            if not server_url:
                return self._error_response(400, "OpenAPI servers URL 未定义")

            # 假设每个工具只定义一个 path 和 method
            path_item_key = next(iter(openapi_spec.get("paths", {})), None)
            if not path_item_key:
                return self._error_response(400, "OpenAPI paths 未定义")
            path_item = openapi_spec["paths"][path_item_key]

            # 假设只有一个方法 (get, post, etc.)
            method = next(iter(path_item), None)
            if not method:
                return self._error_response(400, "OpenAPI method 未定义")
            operation = path_item[method]
            method = method.upper()

            # 2. 准备请求参数
            params = {}
            headers = {"Content-Type": "application/json"}
            path_params = {}
            request_body = {}
            api_key_value = None
            api_key_param_name = None
            api_key_in = None

            # 获取 API Key (如果需要)
            connection_id = tool_config.get("connection_id")
            if connection_id:
                api_key_value = self.connection_manager.get_api_key(connection_id)
                if not api_key_value:
                    return self._error_response(401, f"无法获取API Key: {connection_id}")

            # 分配参数到对应位置
            for param_def in operation.get("parameters", []):
                param_name = param_def.get("name")
                param_in = param_def.get("in")
                is_api_key = param_def.get("x-is-api-key", False)

                if is_api_key:
                    api_key_param_name = param_name
                    api_key_in = param_in
                    continue # API Key 特殊处理，不从 arguments 获取

                if param_name in arguments:
                    value = arguments[param_name]
                    if param_in == "query":
                        params[param_name] = value
                    elif param_in == "path":
                        path_params[param_name] = value
                    elif param_in == "header":
                        headers[param_name] = str(value)
                    # 暂不支持 'cookie'

            # 处理请求体 (如果定义了)
            request_body_def = operation.get("requestBody")
            if request_body_def:
                # 简单处理：假设所有 arguments 都放入请求体
                # 更完善的处理需要检查 requestBody schema
                request_body = arguments.copy()
                # 移除已用于 query/path/header 的参数
                for p_name in list(params.keys()) + list(path_params.keys()) + [h for h in headers if h != 'Content-Type']:
                    request_body.pop(p_name, None)

            # 添加 API Key
            if api_key_value and api_key_param_name and api_key_in:
                if api_key_in == "query":
                    params[api_key_param_name] = api_key_value
                elif api_key_in == "header":
                    headers[api_key_param_name] = api_key_value
                # 暂不支持 path/cookie 中的 API Key

            # 3. 构建完整 URL (替换路径参数)
            url_path = path_item_key
            for name, value in path_params.items():
                url_path = url_path.replace(f"{{{name}}}", str(value))
                
            # 确保 server_url 以 '/' 结尾，以便 urljoin 正确处理相对路径
            if not server_url.endswith('/'):
                server_url += '/'
                
            url = urljoin(server_url, url_path.lstrip('/')) # 同时移除 url_path 开头的 '/' 避免 //

            # 4. 发送请求
            logger.info(f"发送{method}请求到: {url}")
            logger.debug(f"查询参数: {params}")
            logger.debug(f"请求头: {headers}")
            logger.debug(f"请求体: {request_body}")

            response = None
            try:
                if method == "GET":
                    response = requests.get(url, params=params, headers=headers, timeout=10)
                elif method == "POST":
                    response = requests.post(url, params=params, json=request_body, headers=headers, timeout=10)
                # 可以添加 PUT, DELETE 等其他方法
                else:
                    return self._error_response(400, f"不支持的HTTP方法: {method}")
            except requests.exceptions.Timeout:
                logger.error(f"请求超时: {method} {url}")
                return self._error_response(504, f"请求高德API超时: {url}")
            except requests.exceptions.RequestException as e:
                logger.error(f"请求时发生网络错误: {method} {url} - Error: {e}")
                return self._error_response(500, f"请求高德API时发生网络错误: {e}")

            # 5. 处理响应
            logger.info(f"收到响应状态码: {response.status_code}")
            if response.status_code == 200:
                # Log the response body for successful requests
                logger.info(f"收到响应内容 (状态码 200): {response.text[:500]}...") # Log truncated response
            response.raise_for_status()  # 抛出HTTP错误
            response_data = response.json()
            logger.debug(f"收到原始响应数据: {response_data}")

            # 高德API特殊处理 (基于其返回结构)
            if "status" in response_data:
                if response_data["status"] == "1":
                    # 成功
                    return {
                        "code": 0,
                        "message": response_data.get("info", "OK"),
                        "data": self._extract_data(response_data, tool_config.get("name", ""))
                    }
                else:
                    # 失败
                    return {
                        "code": int(response_data.get("infocode", 1)),
                        "message": response_data.get("info", "API错误"),
                        "data": None
                    }
            
            # 通用处理 (如果不是高德格式)
            return {
                "code": 0,
                "message": "OK",
                "data": response_data
            }
            
        except Exception as e:
            logger.error(f"执行工具时发生错误: {str(e)}")
            return self._error_response(500, f"执行工具时发生错误: {str(e)}")
    
    def _error_response(self, code: int, message: str) -> Dict[str, Any]:
        """
        创建标准错误响应
        
        Args:
            code: 错误码
            message: 错误信息
            
        Returns:
            标准化的错误响应字典
        """
        logger.error(message)
        return {
            "code": code,
            "message": message,
            "data": None
        }
        
    def _extract_data(self, response_data: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """
        从API响应中提取相关数据
        
        Args:
            response_data: API响应数据
            tool_name: 工具名称
            
        Returns:
            提取的数据字典
        """
        # 根据工具名称提取相关字段
        if tool_name == "query_amap_district":
            return {
                "districts": response_data.get("districts", []),
                "count": response_data.get("count"),
                "suggestion": response_data.get("suggestion", {})
            }
        elif tool_name == "query_amap_weather":
            return {
                "lives": response_data.get("lives", []),
                "forecasts": response_data.get("forecasts", [])
            }
        
        # 默认返回整个响应
        return response_data


class LocalFunctionConnector:
    """本地函数连接器 - 负责执行本地Python函数"""
    
    def execute(self, tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行本地函数调用
        
        Args:
            tool_config: 工具配置
            arguments: 调用参数
            
        Returns:
            标准化的响应字典 {code, message, data}
        """
        try:
            execution_details = tool_config.get("execution_details", {})
            module_path = execution_details.get("module_path") 
            function_name = execution_details.get("function_name")
            
            if not all([module_path, function_name]):
                return self._error_response(400, "工具配置不完整: 缺少module_path或function_name")
            
            # 动态导入模块和函数
            module = importlib.import_module(module_path)
            function = getattr(module, function_name)
            
            # 执行函数
            result = function(**arguments)
            
            # 如果函数已返回标准格式，直接返回
            if isinstance(result, dict) and all(k in result for k in ["code", "message"]):
                return result
                
            # 否则包装结果
            return {
                "code": 0,
                "message": "OK",
                "data": result
            }
            
        except ImportError as e:
            return self._error_response(500, f"模块导入错误: {str(e)}")
        except AttributeError as e:
            return self._error_response(500, f"函数不存在: {str(e)}")
        except Exception as e:
            return self._error_response(500, f"执行函数时发生错误: {str(e)}")
    
    def _error_response(self, code: int, message: str) -> Dict[str, Any]:
        """创建标准错误响应"""
        logger.error(message)
        return {
            "code": code,
            "message": message,
            "data": None
        }
