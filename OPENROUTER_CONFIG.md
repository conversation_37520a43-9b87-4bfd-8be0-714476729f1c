# OpenRouter 配置指南

本项目已集成 OpenRouter 的供应商优先顺序和备用供应商配置功能。您可以通过环境变量灵活控制 API 调用行为。

## 快速开始

1. **复制配置模板**：
   ```bash
   cp env.template .env
   ```

2. **编辑 .env 文件**，设置您的 API 密钥和偏好配置。

## 核心配置选项

### 1. 供应商优先顺序 (`OPENROUTER_PROVIDER_ORDER`)

控制 OpenRouter 使用供应商的优先级顺序。

```bash
# 示例配置
OPENROUTER_PROVIDER_ORDER=Anthropic,Together,OpenAI
```

**可用供应商**：
- `Anthropic` - Claude 系列模型
- `Together` - 开源模型托管
- `OpenAI` - GPT 系列模型
- `Groq` - 高速推理
- `Fireworks` - 快速 API
- `Nebius` - 云服务
- `Hyperbolic` - 高性能计算

### 2. 备用供应商控制 (`OPENROUTER_ALLOW_FALLBACKS`)

控制是否允许使用备用供应商。

```bash
# 禁用备用供应商（严格按优先顺序）
OPENROUTER_ALLOW_FALLBACKS=false

# 启用备用供应商（允许自动切换）
OPENROUTER_ALLOW_FALLBACKS=true
```

### 3. 忽略供应商列表 (`OPENROUTER_PROVIDER_IGNORE`)

指定不希望使用的供应商。

```bash
# 忽略不稳定的供应商
OPENROUTER_PROVIDER_IGNORE=Fireworks,Nebius,Hyperbolic

# 不忽略任何供应商
OPENROUTER_PROVIDER_IGNORE=
```

### 4. 最大 Token 限制 (`OPENROUTER_MAX_TOKENS`)

设置 API 调用的最大 token 数量。

```bash
# 常用值
OPENROUTER_MAX_TOKENS=32000   # 默认值
OPENROUTER_MAX_TOKENS=16384   # 中等长度
OPENROUTER_MAX_TOKENS=128000  # 长文本处理
```

## 推荐配置组合

### 高质量优先
适合对输出质量要求较高的场景。

```bash
OPENROUTER_PROVIDER_ORDER=Anthropic,OpenAI
OPENROUTER_ALLOW_FALLBACKS=false
OPENROUTER_PROVIDER_IGNORE=Fireworks,Nebius,Hyperbolic
OPENROUTER_MAX_TOKENS=32000
```

### 速度优先
适合需要快速响应的场景。

```bash
OPENROUTER_PROVIDER_ORDER=Groq,Together
OPENROUTER_ALLOW_FALLBACKS=true
OPENROUTER_PROVIDER_IGNORE=
OPENROUTER_MAX_TOKENS=16384
```

### 平衡选择
质量和速度的平衡配置。

```bash
OPENROUTER_PROVIDER_ORDER=Anthropic,Together,OpenAI
OPENROUTER_ALLOW_FALLBACKS=true
OPENROUTER_PROVIDER_IGNORE=Fireworks,Nebius
OPENROUTER_MAX_TOKENS=32000
```

### 成本优化
适合对成本敏感的场景。

```bash
OPENROUTER_PROVIDER_ORDER=Together,Groq
OPENROUTER_ALLOW_FALLBACKS=true
OPENROUTER_PROVIDER_IGNORE=Anthropic,OpenAI
OPENROUTER_MAX_TOKENS=16384
```

## 配置验证

启动应用时，系统会自动验证和记录您的 OpenRouter 配置：

```bash
python run.py
```

查看日志输出中的 "OpenRouter Configuration" 部分，确认配置是否正确应用。

## 实际 API 调用示例

配置生效后，您的 API 调用将自动应用这些设置：

```python
# 系统会自动构建如下请求
response = requests.post(
    url="https://openrouter.ai/api/v1/chat/completions",
    headers={"Authorization": f"Bearer {OPENROUTER_API_KEY}"},
    json={
        "model": "your-model-name",
        "messages": [...],
        "provider": {
            "order": ["Anthropic", "Together"],
            "allow_fallbacks": False,
            "ignore": ["Fireworks", "Nebius", "Hyperbolic"]
        },
        "max_tokens": 32000
    }
)
```

## 故障排除

### 1. 配置不生效
- 确认 `.env` 文件在项目根目录
- 检查环境变量名称拼写
- 重启应用以加载新配置

### 2. 供应商名称错误
- 使用正确的供应商名称（区分大小写）
- 参考上述可用供应商列表

### 3. API 调用失败
- 检查 API 密钥是否正确
- 确认选择的供应商支持您使用的模型
- 查看应用日志获取详细错误信息

## 高级用法

### 动态配置
您可以为不同的功能模块设置不同的配置：

```bash
# 主要 LLM 配置
LLM_PROVIDER=openrouter
LLM_MODEL_NAME=anthropic/claude-3-opus:beta

# 文本分析专用配置
ANALYZE_TEXT_LLM_PROVIDER=openrouter
ANALYZE_TEXT_LLM_MODEL_NAME=deepseek/deepseek-r1
```

### 调试模式
启用详细日志以调试配置问题：

```bash
# 在 .env 中添加
LOGGING_LEVEL=DEBUG
```

这将输出详细的配置解析和 API 调用信息。 