# 多线程Visit处理功能说明

## 概述

本次更新为ICRC表单信息提取功能添加了多线程处理能力，可以并发处理多个visits，显著提升处理性能。

## 功能特性

### 1. 并发处理
- **Visit级别并行**: 每个visit在独立的线程中处理
- **自动线程数选择**: 根据CPU核心数和visits数量自动选择最优线程数
- **线程安全**: 确保多线程环境下的数据一致性

### 2. 智能优化
- **非循环表单早期终止**: 对于非循环表单，一旦找到完整数据，其他线程会自动停止处理
- **资源限制**: 最大线程数限制为10，避免资源过度消耗
- **错误隔离**: 单个visit的错误不会影响其他visits的处理

### 3. 兼容性保证
- **环境变量控制**: 可通过环境变量启用/禁用多线程处理
- **备用方案**: 保留原始串行处理作为备用方案
- **完全向后兼容**: 不影响现有功能和接口

## 配置选项

### 环境变量

```bash
# 启用多线程处理（默认值）
ENABLE_CONCURRENT_VISIT_PROCESSING=true

# 禁用多线程处理，使用串行处理
ENABLE_CONCURRENT_VISIT_PROCESSING=false
```

## 性能提升

### 理论性能提升
- **多visit场景**: 性能提升约为 `min(visit数量, CPU核心数*2, 10)` 倍
- **单visit多data_item场景**: 无性能提升（单个visit内部仍为串行）
- **混合场景**: 根据visit分布情况有不同程度的性能提升

### 实际测试场景
1. **5个visits，每个visit包含2个data_items**: 预期性能提升3-5倍
2. **10个visits，每个visit包含1个data_item**: 预期性能提升8-10倍
3. **2个visits，每个visit包含10个data_items**: 预期性能提升约2倍

## 日志监控

### 新增日志标识
- `TRACE - 线程{thread_id}`: 标识特定线程的操作
- `TRACE - 并发处理visits`: 并发处理开始/结束
- `TRACE - 串行处理visits`: 串行处理开始/结束（备用方案）

### 性能监控
```
TRACE - 并发处理统计: 总visits=5, 成功visits=5, 总结果数=15, 记录映射数=25
TRACE - 结束并发处理visits - 总耗时: 12.3456秒
```

## 使用建议

### 适用场景
✅ **推荐使用多线程**:
- 多个visits需要处理
- 每个visit的处理时间较长
- 系统有足够的CPU资源

❌ **不推荐使用多线程**:
- 只有1-2个visits
- 系统资源紧张
- 需要严格的处理顺序

### 故障排除

如果遇到问题，可以通过以下方式排查：

1. **禁用多线程处理**:
   ```bash
   export ENABLE_CONCURRENT_VISIT_PROCESSING=false
   ```

2. **检查日志中的线程标识**:
   - 查找 `线程{thread_id}` 相关的错误信息
   - 确认是否有线程间的资源竞争

3. **监控系统资源**:
   - CPU使用率
   - 内存使用情况
   - LLM API调用频率限制

## 技术实现

### 核心函数
- `process_visits_concurrently()`: 并发处理主函数
- `process_single_visit()`: 单个visit处理函数
- `process_visits_serially()`: 串行处理备用函数

### 线程同步机制
- `threading.Event`: 用于非循环表单的早期终止通知
- `ThreadPoolExecutor`: 管理线程池和任务分发
- `as_completed()`: 按完成顺序收集结果

### 错误处理
- 单个visit的异常不会影响其他visits
- 完整的异常信息记录和传播
- 优雅的资源清理和释放

## 版本兼容性

- **向前兼容**: 完全兼容现有代码和配置
- **接口不变**: 函数签名和返回值格式保持不变
- **配置可选**: 多线程功能默认启用，但可以禁用

## 注意事项

1. **LLM API限制**: 确保LLM服务提供商支持并发请求
2. **内存使用**: 多线程会增加内存使用量
3. **调试复杂性**: 多线程环境下的调试相对复杂
4. **日志顺序**: 多线程日志可能出现交错，需要通过线程ID区分

## 更新日志

### v1.0.0 (当前版本)
- ✅ 添加visit级别并发处理
- ✅ 实现非循环表单早期终止机制
- ✅ 添加环境变量控制开关
- ✅ 保留串行处理备用方案
- ✅ 完善日志和错误处理
- ✅ 添加性能监控和统计 